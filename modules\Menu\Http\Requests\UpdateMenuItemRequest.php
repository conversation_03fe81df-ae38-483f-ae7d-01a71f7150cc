<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'category_id' => 'nullable|exists:menu_categories,id',
            'name' => 'sometimes|string|max:255',
            'code' => 'sometimes|string|max:50',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string',
            'base_price' => 'sometimes|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'string|max:500',
            'prep_time_minutes' => 'integer|min:0',
            'calories' => 'nullable|integer|min:0',
            'nutritional_info' => 'nullable|array',
            'allergens' => 'nullable|array',
            'dietary_info' => 'nullable|array',
            'recipe_id' => 'nullable|exists:recipes,id',
            'barcode' => 'nullable|string|max:100',
            'sku' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_spicy' => 'boolean',
            'spice_level' => 'nullable|integer|between:1,5',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_id.exists' => 'الفئة المحددة غير موجودة',
            'name.string' => 'اسم العنصر يجب أن يكون نص',
            'name.max' => 'اسم العنصر يجب ألا يتجاوز 255 حرف',
            'code.string' => 'كود العنصر يجب أن يكون نص',
            'code.max' => 'كود العنصر يجب ألا يتجاوز 50 حرف',
            'description.string' => 'الوصف يجب أن يكون نص',
            'short_description.string' => 'الوصف المختصر يجب أن يكون نص',
            'base_price.numeric' => 'السعر الأساسي يجب أن يكون رقم',
            'base_price.min' => 'السعر الأساسي يجب أن يكون على الأقل 0',
            'cost_price.numeric' => 'سعر التكلفة يجب أن يكون رقم',
            'cost_price.min' => 'سعر التكلفة يجب أن يكون على الأقل 0',
            'image_urls.array' => 'روابط الصور يجب أن تكون مصفوفة',
            'image_urls.*.string' => 'رابط الصورة يجب أن يكون نص',
            'image_urls.*.max' => 'رابط الصورة يجب ألا يتجاوز 500 حرف',
            'prep_time_minutes.integer' => 'وقت التحضير يجب أن يكون رقم صحيح',
            'prep_time_minutes.min' => 'وقت التحضير يجب أن يكون على الأقل 0',
            'calories.integer' => 'السعرات الحرارية يجب أن تكون رقم صحيح',
            'calories.min' => 'السعرات الحرارية يجب أن تكون على الأقل 0',
            'nutritional_info.array' => 'المعلومات الغذائية يجب أن تكون مصفوفة',
            'allergens.array' => 'مسببات الحساسية يجب أن تكون مصفوفة',
            'dietary_info.array' => 'المعلومات الغذائية يجب أن تكون مصفوفة',
            'recipe_id.exists' => 'الوصفة المحددة غير موجودة',
            'barcode.string' => 'الباركود يجب أن يكون نص',
            'barcode.max' => 'الباركود يجب ألا يتجاوز 100 حرف',
            'sku.string' => 'رمز المنتج يجب أن يكون نص',
            'sku.max' => 'رمز المنتج يجب ألا يتجاوز 100 حرف',
            'is_active.boolean' => 'حالة النشاط يجب أن تكون صحيح أو خطأ',
            'is_featured.boolean' => 'حالة المميز يجب أن تكون صحيح أو خطأ',
            'is_spicy.boolean' => 'حالة الحار يجب أن تكون صحيح أو خطأ',
            'spice_level.integer' => 'مستوى الحرارة يجب أن يكون رقم صحيح',
            'spice_level.between' => 'مستوى الحرارة يجب أن يكون بين 1 و 5',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}