<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'menu_id' => 'required|exists:menus,id',
            'parent_category_id' => 'nullable|exists:menu_categories,id',
            'name' => 'required|string|max:100',
            'icon' => 'nullable|string|max:100',
            'code' => 'required|string|max:50',
            'description' => 'nullable|string',
            'image_url' => 'nullable|string|max:500',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_id.required' => 'معرف القائمة مطلوب',
            'menu_id.exists' => 'القائمة المحددة غير موجودة',
            'parent_category_id.exists' => 'الفئة الأب المحددة غير موجودة',
            'name.required' => 'اسم الفئة مطلوب',
            'name.string' => 'اسم الفئة يجب أن يكون نص',
            'name.max' => 'اسم الفئة يجب ألا يتجاوز 100 حرف',
            'code.required' => 'كود الفئة مطلوب',
            'code.string' => 'كود الفئة يجب أن يكون نص',
            'code.max' => 'كود الفئة يجب ألا يتجاوز 50 حرف',
            'icon.string' => 'الأيقونة يجب أن تكون نص',
            'icon.max' => 'الأيقونة يجب ألا تتجاوز 100 حرف',
            'image_url.string' => 'رابط الصورة يجب أن يكون نص',
            'image_url.max' => 'رابط الصورة يجب ألا يتجاوز 500 حرف',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}