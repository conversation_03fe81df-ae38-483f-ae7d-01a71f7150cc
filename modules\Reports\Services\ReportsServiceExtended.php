<?php

namespace Modules\Reports\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

trait ReportsServiceExtended
{
    /**
     * Generate Customer Report
     */
    public function generateCustomerReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        // Get customer data
        $customerData = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->leftJoin('customers as c', 'o.customer_id', '=', 'c.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $customerData->where('o.branch_id', $branchId);
        }

        // Top customers by spending
        $topCustomers = $customerData
            ->whereNotNull('o.customer_id')
            ->select([
                'c.id as customer_id',
                'c.name as customer_name',
                'c.email',
                'c.phone',
                DB::raw('COUNT(o.id) as total_orders'),
                DB::raw('SUM(o.total_amount) as total_spent'),
                DB::raw('AVG(o.total_amount) as average_order_value'),
                DB::raw('MAX(o.created_at) as last_order_date')
            ])
            ->groupBy(['c.id', 'c.name', 'c.email', 'c.phone'])
            ->orderBy('total_spent', 'desc')
            ->limit(20)
            ->get();

        // New vs returning customers
        $newCustomers = DB::table('customers as c')
            ->join('orders as o', 'c.id', '=', 'o.customer_id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('c.created_at', [$startDateTime, $endDateTime]);

        if ($branchId) {
            $newCustomers->where('o.branch_id', $branchId);
        }

        $newCustomerCount = $newCustomers->distinct('c.id')->count();

        // Total unique customers in period
        $totalUniqueCustomers = $customerData
            ->whereNotNull('o.customer_id')
            ->distinct('o.customer_id')
            ->count();

        $returningCustomers = $totalUniqueCustomers - $newCustomerCount;

        // Guest orders (no customer account)
        $guestOrders = $customerData
            ->whereNull('o.customer_id')
            ->count();

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_unique_customers' => $totalUniqueCustomers,
                'new_customers' => $newCustomerCount,
                'returning_customers' => $returningCustomers,
                'guest_orders' => $guestOrders,
            ],
            'top_customers' => $topCustomers->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Stock Movement Report
     */
    public function generateStockMovement(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $movementsQuery = DB::table('inventory_movements as im')
            ->join('branches as b', 'im.branch_id', '=', 'b.id')
            ->join('products as p', 'im.product_id', '=', 'p.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('im.movement_date', [$startDateTime, $endDateTime]);

        if ($branchId) {
            $movementsQuery->where('im.branch_id', $branchId);
        }

        $movements = $movementsQuery
            ->select([
                'p.name as item_name',
                'p.sku',
                'p.unit',
                'im.movement_type',
                'im.quantity',
                'im.unit_cost',
                'im.total_cost',
                'im.notes as reason',
                'im.movement_date as performed_at',
                'im.branch_id'
            ])
            ->orderBy('im.movement_date', 'desc')
            ->get();

        // Group by movement type
        $movementsByType = $movements->groupBy('movement_type')->map(function ($items, $type) {
            return [
                'type' => $type,
                'count' => $items->count(),
                'total_quantity' => $items->sum('quantity'),
                'total_value' => round($items->sum('total_cost'), 2),
            ];
        });

        // Inbound vs Outbound
        $inboundTypes = ['add', 'initial_stock', 'return', 'transfer_in', 'adjustment_in'];
        $outboundTypes = ['subtract', 'waste', 'transfer_out', 'consumption', 'adjustment_out'];

        $inboundMovements = $movements->whereIn('movement_type', $inboundTypes);
        $outboundMovements = $movements->whereIn('movement_type', $outboundTypes);

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_movements' => $movements->count(),
                'inbound_movements' => $inboundMovements->count(),
                'outbound_movements' => $outboundMovements->count(),
                'inbound_value' => round($inboundMovements->sum('total_cost'), 2),
                'outbound_value' => round($outboundMovements->sum('total_cost'), 2),
            ],
            'movements_by_type' => $movementsByType->values()->toArray(),
            'detailed_movements' => $movements->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Reorder Point Report
     */
    public function generateReorderPoint(int $tenantId, ?int $branchId): array
    {
        $inventoryQuery = DB::table('branch_inventories as bi')
            ->join('branches as b', 'bi.branch_id', '=', 'b.id')
            ->join('products as p', 'bi.product_id', '=', 'p.id')
            ->where('b.tenant_id', $tenantId);

        if ($branchId) {
            $inventoryQuery->where('bi.branch_id', $branchId);
        }

        $inventoryItems = $inventoryQuery
            ->select([
                'p.name as item_name',
                'p.sku',
                'p.unit',
                'bi.current_stock',
                'bi.minimum_level as minimum_stock',
                'bi.maximum_level as maximum_stock',
                'bi.reorder_point',
                'bi.branch_id',
                DB::raw('CASE
                    WHEN bi.current_stock <= 0 THEN "out_of_stock"
                    WHEN bi.current_stock <= bi.reorder_point THEN "reorder_needed"
                    WHEN bi.current_stock <= bi.minimum_level THEN "low_stock"
                    ELSE "adequate"
                END as stock_status')
            ])
            ->get();

        // Group by stock status
        $stockByStatus = $inventoryItems->groupBy('stock_status')->map(function ($items, $status) {
            return [
                'status' => $status,
                'count' => $items->count(),
                'items' => $items->toArray(),
            ];
        });

        // Items needing reorder
        $reorderItems = $inventoryItems->filter(function ($item) {
            return in_array($item->stock_status, ['out_of_stock', 'reorder_needed']);
        });

        return [
            'branch_id' => $branchId,
            'summary' => [
                'total_items' => $inventoryItems->count(),
                'out_of_stock' => $inventoryItems->where('stock_status', 'out_of_stock')->count(),
                'reorder_needed' => $inventoryItems->where('stock_status', 'reorder_needed')->count(),
                'low_stock' => $inventoryItems->where('stock_status', 'low_stock')->count(),
                'adequate_stock' => $inventoryItems->where('stock_status', 'adequate')->count(),
            ],
            'stock_by_status' => $stockByStatus->values()->toArray(),
            'reorder_recommendations' => $reorderItems->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Menu Items Report
     */
    public function generateMenuItemsReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $menuItemsQuery = DB::table('order_items as oi')
            ->join('orders as o', 'oi.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('menu_items as mi', 'oi.menu_item_id', '=', 'mi.id')
            ->join('menu_categories as mc', 'mi.category_id', '=', 'mc.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $menuItemsQuery->where('o.branch_id', $branchId);
        }

        $menuItemStats = $menuItemsQuery
            ->select([
                'mi.id as menu_item_id',
                'mi.name as item_name',
                'mi.sku',
                'mc.name as category',
                DB::raw('SUM(oi.quantity) as total_ordered'),
                DB::raw('SUM(oi.total_price) as total_revenue'),
                DB::raw('COUNT(DISTINCT o.id) as order_frequency'),
                DB::raw('AVG(oi.unit_price) as average_price')
            ])
            ->groupBy(['mi.id', 'mi.name', 'mi.sku', 'mc.name'])
            ->orderBy('total_ordered', 'desc')
            ->get();

        // Get branch-specific pricing if available
        if ($branchId) {
            $branchPricing = DB::table('menu_item_branches as mib')
                ->where('branch_id', $branchId)
                ->pluck('branch_price', 'menu_item_id');

            $menuItemStats = $menuItemStats->map(function ($item) use ($branchPricing) {
                $item->branch_price = $branchPricing[$item->menu_item_id] ?? null;
                return $item;
            });
        }

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_menu_items_ordered' => $menuItemStats->count(),
                'total_quantity_ordered' => $menuItemStats->sum('total_ordered'),
                'total_revenue' => round($menuItemStats->sum('total_revenue'), 2),
            ],
            'most_ordered_items' => $menuItemStats->take(20)->toArray(),
            'all_items' => $menuItemStats->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Meal Time Sales Report
     */
    public function generateMealTimeSales(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $ordersQuery = $this->getOrdersQuery($tenantId, $branchId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->where('status', '!=', 'cancelled');

        $orders = $ordersQuery->get();

        // Categorize orders by meal time
        $mealTimeSales = $orders->groupBy(function ($order) {
            $hour = Carbon::parse($order->created_at)->hour;
            
            if ($hour >= 6 && $hour < 11) {
                return 'breakfast';
            } elseif ($hour >= 11 && $hour < 16) {
                return 'lunch';
            } elseif ($hour >= 16 && $hour < 22) {
                return 'dinner';
            } else {
                return 'late_night';
            }
        })->map(function ($orders, $mealTime) {
            return [
                'meal_time' => $mealTime,
                'order_count' => $orders->count(),
                'total_sales' => round($orders->sum('total_amount'), 2),
                'average_order_value' => round($orders->avg('total_amount'), 2),
            ];
        });

        $totalSales = $orders->sum('total_amount');

        // Add percentage of total sales
        $mealTimeSales = $mealTimeSales->map(function ($mealTime) use ($totalSales) {
            $mealTime['percentage_of_sales'] = $totalSales > 0 ? round(($mealTime['total_sales'] / $totalSales) * 100, 2) : 0;
            return $mealTime;
        });

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_orders' => $orders->count(),
                'total_sales' => round($totalSales, 2),
            ],
            'meal_time_breakdown' => $mealTimeSales->values()->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate tax report
     */
    public function generateTaxReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getDateRange($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $taxQuery = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $taxQuery->where('o.branch_id', $branchId);
        }

        $taxSummary = $taxQuery
            ->select([
                DB::raw('SUM(o.subtotal) as subtotal'),
                DB::raw('SUM(o.tax_amount) as total_tax'),
                DB::raw('SUM(o.total_amount) as total_with_tax'),
                DB::raw('COUNT(o.id) as order_count'),
                DB::raw('AVG(o.tax_rate) as average_tax_rate')
            ])
            ->first();

        $taxByRate = $taxQuery
            ->select([
                'o.tax_rate',
                DB::raw('COUNT(o.id) as order_count'),
                DB::raw('SUM(o.subtotal) as subtotal'),
                DB::raw('SUM(o.tax_amount) as tax_amount'),
                DB::raw('SUM(o.total_amount) as total_amount')
            ])
            ->groupBy('o.tax_rate')
            ->orderBy('o.tax_rate')
            ->get();

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_orders' => $taxSummary->order_count ?? 0,
                'subtotal' => round($taxSummary->subtotal ?? 0, 2),
                'total_tax_collected' => round($taxSummary->total_tax ?? 0, 2),
                'total_with_tax' => round($taxSummary->total_with_tax ?? 0, 2),
                'average_tax_rate' => round($taxSummary->average_tax_rate ?? 0, 2),
            ],
            'tax_breakdown' => $taxByRate->map(function ($item) {
                return [
                    'tax_rate' => $item->tax_rate,
                    'order_count' => $item->order_count,
                    'subtotal' => round($item->subtotal, 2),
                    'tax_amount' => round($item->tax_amount, 2),
                    'total_amount' => round($item->total_amount, 2),
                ];
            })->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate void/cancelled orders report
     */
    public function generateVoidCancelledReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getDateRange($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $voidQuery = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->leftJoin('users as u', 'o.cancelled_by', '=', 'u.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->whereIn('o.status', ['cancelled', 'void']);

        if ($branchId) {
            $voidQuery->where('o.branch_id', $branchId);
        }

        $voidOrders = $voidQuery
            ->select([
                'o.id',
                'o.order_number',
                'o.status',
                'o.total_amount',
                'o.cancellation_reason',
                'o.created_at',
                'o.cancelled_at',
                'u.name as cancelled_by_name',
                'b.name as branch_name'
            ])
            ->orderBy('o.cancelled_at', 'desc')
            ->get();

        $summary = [
            'total_void_cancelled' => $voidOrders->count(),
            'total_amount_lost' => $voidOrders->sum('total_amount'),
            'cancelled_count' => $voidOrders->where('status', 'cancelled')->count(),
            'void_count' => $voidOrders->where('status', 'void')->count(),
        ];

        $reasonBreakdown = $voidOrders->groupBy('cancellation_reason')->map(function ($items, $reason) {
            return [
                'reason' => $reason ?: 'No reason provided',
                'count' => $items->count(),
                'total_amount' => round($items->sum('total_amount'), 2),
            ];
        })->values();

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_void_cancelled' => $summary['total_void_cancelled'],
                'total_amount_lost' => round($summary['total_amount_lost'], 2),
                'cancelled_orders' => $summary['cancelled_count'],
                'void_orders' => $summary['void_count'],
            ],
            'reason_breakdown' => $reasonBreakdown->toArray(),
            'void_cancelled_orders' => $voidOrders->map(function ($order) {
                return [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'status' => $order->status,
                    'total_amount' => round($order->total_amount, 2),
                    'reason' => $order->cancellation_reason ?: 'No reason provided',
                    'created_at' => $order->created_at,
                    'cancelled_at' => $order->cancelled_at,
                    'cancelled_by' => $order->cancelled_by_name,
                    'branch' => $order->branch_name,
                ];
            })->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate hourly sales report
     */
    public function generateHourlySalesReport(int $tenantId, ?int $branchId, string $date): array
    {
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        $hourlySalesQuery = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDate, $endDate])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $hourlySalesQuery->where('o.branch_id', $branchId);
        }

        $hourlySales = $hourlySalesQuery
            ->select([
                DB::raw('HOUR(o.created_at) as hour'),
                DB::raw('COUNT(o.id) as order_count'),
                DB::raw('SUM(o.total_amount) as total_sales'),
                DB::raw('AVG(o.total_amount) as average_order_value')
            ])
            ->groupBy(DB::raw('HOUR(o.created_at)'))
            ->orderBy('hour')
            ->get();

        // Fill missing hours with zero values
        $hourlyData = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $existingData = $hourlySales->firstWhere('hour', $hour);
            $hourlyData[] = [
                'hour' => $hour,
                'hour_display' => sprintf('%02d:00', $hour),
                'order_count' => $existingData ? $existingData->order_count : 0,
                'total_sales' => $existingData ? round($existingData->total_sales, 2) : 0,
                'average_order_value' => $existingData ? round($existingData->average_order_value, 2) : 0,
            ];
        }

        $peakHour = collect($hourlyData)->sortByDesc('total_sales')->first();
        $totalSales = collect($hourlyData)->sum('total_sales');
        $totalOrders = collect($hourlyData)->sum('order_count');

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'summary' => [
                'total_sales' => round($totalSales, 2),
                'total_orders' => $totalOrders,
                'average_hourly_sales' => $totalSales > 0 ? round($totalSales / 24, 2) : 0,
                'peak_hour' => $peakHour['hour_display'] ?? '00:00',
                'peak_hour_sales' => $peakHour['total_sales'] ?? 0,
            ],
            'hourly_breakdown' => $hourlyData,
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate table turnover report (for dine-in restaurants)
     */
    public function generateTableTurnoverReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getDateRange($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $tableQuery = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->leftJoin('tables as t', 'o.table_id', '=', 't.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled')
            ->whereNotNull('o.table_id');

        if ($branchId) {
            $tableQuery->where('o.branch_id', $branchId);
        }

        $tableStats = $tableQuery
            ->select([
                't.id as table_id',
                't.table_number',
                't.capacity',
                DB::raw('COUNT(o.id) as total_orders'),
                DB::raw('SUM(o.total_amount) as total_revenue'),
                DB::raw('AVG(TIMESTAMPDIFF(MINUTE, o.created_at, o.completed_at)) as avg_dining_time'),
                DB::raw('SUM(o.customer_count) as total_customers_served')
            ])
            ->groupBy(['t.id', 't.table_number', 't.capacity'])
            ->orderBy('total_revenue', 'desc')
            ->get();

        $days = Carbon::parse($start)->diffInDays(Carbon::parse($end)) + 1;

        $tableData = $tableStats->map(function ($table) use ($days) {
            $avgTurnoversPerDay = $days > 0 ? round($table->total_orders / $days, 2) : 0;
            $revenuePerSeat = $table->capacity > 0 ? round($table->total_revenue / $table->capacity, 2) : 0;

            return [
                'table_id' => $table->table_id,
                'table_number' => $table->table_number,
                'capacity' => $table->capacity,
                'total_orders' => $table->total_orders,
                'total_revenue' => round($table->total_revenue, 2),
                'average_dining_time_minutes' => round($table->avg_dining_time ?? 0, 0),
                'total_customers_served' => $table->total_customers_served,
                'average_turnovers_per_day' => $avgTurnoversPerDay,
                'revenue_per_seat' => $revenuePerSeat,
            ];
        });

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_tables_used' => $tableData->count(),
                'total_table_orders' => $tableData->sum('total_orders'),
                'total_table_revenue' => round($tableData->sum('total_revenue'), 2),
                'average_dining_time' => round($tableData->avg('average_dining_time_minutes'), 0),
                'average_daily_turnovers' => round($tableData->avg('average_turnovers_per_day'), 2),
            ],
            'table_performance' => $tableData->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate waste tracking report
     */
    public function generateWasteTrackingReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getDateRange($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $wasteQuery = DB::table('inventory_movements as im')
            ->join('branches as b', 'im.branch_id', '=', 'b.id')
            ->join('products as p', 'im.product_id', '=', 'p.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('im.movement_date', [$startDateTime, $endDateTime])
            ->where('im.movement_type', 'waste');

        if ($branchId) {
            $wasteQuery->where('im.branch_id', $branchId);
        }

        $wasteData = $wasteQuery
            ->select([
                'p.name as product_name',
                'p.sku',
                'p.unit',
                DB::raw('SUM(ABS(im.quantity)) as total_waste_quantity'),
                DB::raw('SUM(ABS(im.total_cost)) as total_waste_cost'),
                DB::raw('COUNT(im.id) as waste_incidents'),
                'im.notes as waste_reason'
            ])
            ->groupBy(['p.id', 'p.name', 'p.sku', 'p.unit', 'im.notes'])
            ->orderBy('total_waste_cost', 'desc')
            ->get();

        $wasteByReason = $wasteData->groupBy('waste_reason')->map(function ($items, $reason) {
            return [
                'reason' => $reason ?: 'No reason specified',
                'total_cost' => round($items->sum('total_waste_cost'), 2),
                'total_quantity' => $items->sum('total_waste_quantity'),
                'incident_count' => $items->sum('waste_incidents'),
            ];
        })->values();

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_waste_cost' => round($wasteData->sum('total_waste_cost'), 2),
                'total_waste_incidents' => $wasteData->sum('waste_incidents'),
                'most_wasted_product' => $wasteData->first()->product_name ?? 'N/A',
                'average_waste_per_incident' => $wasteData->sum('waste_incidents') > 0 ?
                    round($wasteData->sum('total_waste_cost') / $wasteData->sum('waste_incidents'), 2) : 0,
            ],
            'waste_by_product' => $wasteData->map(function ($item) {
                return [
                    'product_name' => $item->product_name,
                    'sku' => $item->sku,
                    'unit' => $item->unit,
                    'total_waste_quantity' => $item->total_waste_quantity,
                    'total_waste_cost' => round($item->total_waste_cost, 2),
                    'waste_incidents' => $item->waste_incidents,
                    'reason' => $item->waste_reason ?: 'No reason specified',
                ];
            })->toArray(),
            'waste_by_reason' => $wasteByReason->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate supplier performance report
     */
    public function generateSupplierPerformanceReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getDateRange($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $supplierQuery = DB::table('purchase_orders as po')
            ->join('branches as b', 'po.branch_id', '=', 'b.id')
            ->join('suppliers as s', 'po.supplier_id', '=', 's.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('po.created_at', [$startDateTime, $endDateTime]);

        if ($branchId) {
            $supplierQuery->where('po.branch_id', $branchId);
        }

        $supplierStats = $supplierQuery
            ->select([
                's.id as supplier_id',
                's.name as supplier_name',
                's.contact_person',
                's.phone',
                DB::raw('COUNT(po.id) as total_orders'),
                DB::raw('SUM(po.total_amount) as total_spent'),
                DB::raw('AVG(po.total_amount) as average_order_value'),
                DB::raw('SUM(CASE WHEN po.status = "delivered" THEN 1 ELSE 0 END) as delivered_orders'),
                DB::raw('SUM(CASE WHEN po.delivery_date <= po.expected_delivery_date THEN 1 ELSE 0 END) as on_time_deliveries'),
                DB::raw('AVG(DATEDIFF(po.delivery_date, po.created_at)) as avg_delivery_days')
            ])
            ->groupBy(['s.id', 's.name', 's.contact_person', 's.phone'])
            ->orderBy('total_spent', 'desc')
            ->get();

        $supplierData = $supplierStats->map(function ($supplier) {
            $deliveryRate = $supplier->total_orders > 0 ?
                round(($supplier->delivered_orders / $supplier->total_orders) * 100, 2) : 0;
            $onTimeRate = $supplier->delivered_orders > 0 ?
                round(($supplier->on_time_deliveries / $supplier->delivered_orders) * 100, 2) : 0;

            return [
                'supplier_id' => $supplier->supplier_id,
                'supplier_name' => $supplier->supplier_name,
                'contact_person' => $supplier->contact_person,
                'phone' => $supplier->phone,
                'total_orders' => $supplier->total_orders,
                'total_spent' => round($supplier->total_spent, 2),
                'average_order_value' => round($supplier->average_order_value, 2),
                'delivery_rate_percent' => $deliveryRate,
                'on_time_delivery_rate_percent' => $onTimeRate,
                'average_delivery_days' => round($supplier->avg_delivery_days ?? 0, 1),
            ];
        });

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_suppliers' => $supplierData->count(),
                'total_purchase_orders' => $supplierData->sum('total_orders'),
                'total_amount_spent' => round($supplierData->sum('total_spent'), 2),
                'average_delivery_rate' => round($supplierData->avg('delivery_rate_percent'), 2),
                'average_on_time_rate' => round($supplierData->avg('on_time_delivery_rate_percent'), 2),
            ],
            'supplier_performance' => $supplierData->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }
}
