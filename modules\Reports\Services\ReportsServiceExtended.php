<?php

namespace Modules\Reports\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

trait ReportsServiceExtended
{
    /**
     * Generate Customer Report
     */
    public function generateCustomerReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        // Get customer data
        $customerData = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->leftJoin('customers as c', 'o.customer_id', '=', 'c.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $customerData->where('o.branch_id', $branchId);
        }

        // Top customers by spending
        $topCustomers = $customerData
            ->whereNotNull('o.customer_id')
            ->select([
                'c.id as customer_id',
                'c.name as customer_name',
                'c.email',
                'c.phone',
                DB::raw('COUNT(o.id) as total_orders'),
                DB::raw('SUM(o.total_amount) as total_spent'),
                DB::raw('AVG(o.total_amount) as average_order_value'),
                DB::raw('MAX(o.created_at) as last_order_date')
            ])
            ->groupBy(['c.id', 'c.name', 'c.email', 'c.phone'])
            ->orderBy('total_spent', 'desc')
            ->limit(20)
            ->get();

        // New vs returning customers
        $newCustomers = DB::table('customers as c')
            ->join('orders as o', 'c.id', '=', 'o.customer_id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('c.created_at', [$startDateTime, $endDateTime]);

        if ($branchId) {
            $newCustomers->where('o.branch_id', $branchId);
        }

        $newCustomerCount = $newCustomers->distinct('c.id')->count();

        // Total unique customers in period
        $totalUniqueCustomers = $customerData
            ->whereNotNull('o.customer_id')
            ->distinct('o.customer_id')
            ->count();

        $returningCustomers = $totalUniqueCustomers - $newCustomerCount;

        // Guest orders (no customer account)
        $guestOrders = $customerData
            ->whereNull('o.customer_id')
            ->count();

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_unique_customers' => $totalUniqueCustomers,
                'new_customers' => $newCustomerCount,
                'returning_customers' => $returningCustomers,
                'guest_orders' => $guestOrders,
            ],
            'top_customers' => $topCustomers->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Stock Movement Report
     */
    public function generateStockMovement(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $movementsQuery = DB::table('inventory_movements as im')
            ->join('branches as b', 'im.branch_id', '=', 'b.id')
            ->join('products as p', 'im.product_id', '=', 'p.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('im.movement_date', [$startDateTime, $endDateTime]);

        if ($branchId) {
            $movementsQuery->where('im.branch_id', $branchId);
        }

        $movements = $movementsQuery
            ->select([
                'p.name as item_name',
                'p.sku',
                'p.unit',
                'im.movement_type',
                'im.quantity',
                'im.unit_cost',
                'im.total_cost',
                'im.notes as reason',
                'im.movement_date as performed_at',
                'im.branch_id'
            ])
            ->orderBy('im.movement_date', 'desc')
            ->get();

        // Group by movement type
        $movementsByType = $movements->groupBy('movement_type')->map(function ($items, $type) {
            return [
                'type' => $type,
                'count' => $items->count(),
                'total_quantity' => $items->sum('quantity'),
                'total_value' => round($items->sum('total_cost'), 2),
            ];
        });

        // Inbound vs Outbound
        $inboundTypes = ['add', 'initial_stock', 'return', 'transfer_in', 'adjustment_in'];
        $outboundTypes = ['subtract', 'waste', 'transfer_out', 'consumption', 'adjustment_out'];

        $inboundMovements = $movements->whereIn('movement_type', $inboundTypes);
        $outboundMovements = $movements->whereIn('movement_type', $outboundTypes);

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_movements' => $movements->count(),
                'inbound_movements' => $inboundMovements->count(),
                'outbound_movements' => $outboundMovements->count(),
                'inbound_value' => round($inboundMovements->sum('total_cost'), 2),
                'outbound_value' => round($outboundMovements->sum('total_cost'), 2),
            ],
            'movements_by_type' => $movementsByType->values()->toArray(),
            'detailed_movements' => $movements->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Reorder Point Report
     */
    public function generateReorderPoint(int $tenantId, ?int $branchId): array
    {
        $inventoryQuery = DB::table('branch_inventories as bi')
            ->join('branches as b', 'bi.branch_id', '=', 'b.id')
            ->join('products as p', 'bi.product_id', '=', 'p.id')
            ->where('b.tenant_id', $tenantId);

        if ($branchId) {
            $inventoryQuery->where('bi.branch_id', $branchId);
        }

        $inventoryItems = $inventoryQuery
            ->select([
                'p.name as item_name',
                'p.sku',
                'p.unit',
                'bi.current_stock',
                'bi.minimum_level as minimum_stock',
                'bi.maximum_level as maximum_stock',
                'bi.reorder_point',
                'bi.branch_id',
                DB::raw('CASE
                    WHEN bi.current_stock <= 0 THEN "out_of_stock"
                    WHEN bi.current_stock <= bi.reorder_point THEN "reorder_needed"
                    WHEN bi.current_stock <= bi.minimum_level THEN "low_stock"
                    ELSE "adequate"
                END as stock_status')
            ])
            ->get();

        // Group by stock status
        $stockByStatus = $inventoryItems->groupBy('stock_status')->map(function ($items, $status) {
            return [
                'status' => $status,
                'count' => $items->count(),
                'items' => $items->toArray(),
            ];
        });

        // Items needing reorder
        $reorderItems = $inventoryItems->filter(function ($item) {
            return in_array($item->stock_status, ['out_of_stock', 'reorder_needed']);
        });

        return [
            'branch_id' => $branchId,
            'summary' => [
                'total_items' => $inventoryItems->count(),
                'out_of_stock' => $inventoryItems->where('stock_status', 'out_of_stock')->count(),
                'reorder_needed' => $inventoryItems->where('stock_status', 'reorder_needed')->count(),
                'low_stock' => $inventoryItems->where('stock_status', 'low_stock')->count(),
                'adequate_stock' => $inventoryItems->where('stock_status', 'adequate')->count(),
            ],
            'stock_by_status' => $stockByStatus->values()->toArray(),
            'reorder_recommendations' => $reorderItems->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Menu Items Report
     */
    public function generateMenuItemsReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $menuItemsQuery = DB::table('order_items as oi')
            ->join('orders as o', 'oi.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('menu_items as mi', 'oi.menu_item_id', '=', 'mi.id')
            ->join('menu_categories as mc', 'mi.category_id', '=', 'mc.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $menuItemsQuery->where('o.branch_id', $branchId);
        }

        $menuItemStats = $menuItemsQuery
            ->select([
                'mi.id as menu_item_id',
                'mi.name as item_name',
                'mi.sku',
                'mc.name as category',
                DB::raw('SUM(oi.quantity) as total_ordered'),
                DB::raw('SUM(oi.total_price) as total_revenue'),
                DB::raw('COUNT(DISTINCT o.id) as order_frequency'),
                DB::raw('AVG(oi.unit_price) as average_price')
            ])
            ->groupBy(['mi.id', 'mi.name', 'mi.sku', 'mc.name'])
            ->orderBy('total_ordered', 'desc')
            ->get();

        // Get branch-specific pricing if available
        if ($branchId) {
            $branchPricing = DB::table('menu_item_branches as mib')
                ->where('branch_id', $branchId)
                ->pluck('branch_price', 'menu_item_id');

            $menuItemStats = $menuItemStats->map(function ($item) use ($branchPricing) {
                $item->branch_price = $branchPricing[$item->menu_item_id] ?? null;
                return $item;
            });
        }

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_menu_items_ordered' => $menuItemStats->count(),
                'total_quantity_ordered' => $menuItemStats->sum('total_ordered'),
                'total_revenue' => round($menuItemStats->sum('total_revenue'), 2),
            ],
            'most_ordered_items' => $menuItemStats->take(20)->toArray(),
            'all_items' => $menuItemStats->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Meal Time Sales Report
     */
    public function generateMealTimeSales(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $ordersQuery = $this->getOrdersQuery($tenantId, $branchId)
            ->whereBetween('created_at', [$startDateTime, $endDateTime])
            ->where('status', '!=', 'cancelled');

        $orders = $ordersQuery->get();

        // Categorize orders by meal time
        $mealTimeSales = $orders->groupBy(function ($order) {
            $hour = Carbon::parse($order->created_at)->hour;
            
            if ($hour >= 6 && $hour < 11) {
                return 'breakfast';
            } elseif ($hour >= 11 && $hour < 16) {
                return 'lunch';
            } elseif ($hour >= 16 && $hour < 22) {
                return 'dinner';
            } else {
                return 'late_night';
            }
        })->map(function ($orders, $mealTime) {
            return [
                'meal_time' => $mealTime,
                'order_count' => $orders->count(),
                'total_sales' => round($orders->sum('total_amount'), 2),
                'average_order_value' => round($orders->avg('total_amount'), 2),
            ];
        });

        $totalSales = $orders->sum('total_amount');

        // Add percentage of total sales
        $mealTimeSales = $mealTimeSales->map(function ($mealTime) use ($totalSales) {
            $mealTime['percentage_of_sales'] = $totalSales > 0 ? round(($mealTime['total_sales'] / $totalSales) * 100, 2) : 0;
            return $mealTime;
        });

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_orders' => $orders->count(),
                'total_sales' => round($totalSales, 2),
            ],
            'meal_time_breakdown' => $mealTimeSales->values()->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }
}
