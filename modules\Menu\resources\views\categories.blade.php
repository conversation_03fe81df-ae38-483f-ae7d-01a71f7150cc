@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة الفئات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ فئات القوائم</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-category-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة الفئات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع فئات القوائم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="categories-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم الفئة</th>
                                <th class="border-bottom-0">اسم الفئة (إنجليزي)</th>
                                <th class="border-bottom-0">القائمة</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">ترتيب العرض</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">إضافة فئة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="category_id" name="category_id">
                    
                    <!-- Basic Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">اسم الفئة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code">كود الفئة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="code" name="code" required maxlength="50">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="menu_id">القائمة <span class="text-danger">*</span></label>
                                        <select class="form-control select2" id="menu_id" name="menu_id" required>
                                            <option value="">اختر القائمة</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="parent_category_id">الفئة الأب</label>
                                        <select class="form-control select2" id="parent_category_id" name="parent_category_id">
                                            <option value="">لا توجد فئة أب</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="icon">الأيقونة</label>
                                        <input type="text" class="form-control" id="icon" name="icon" maxlength="100" placeholder="مثال: mdi-food">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="image_url">رابط الصورة</label>
                                        <input type="url" class="form-control" id="image_url" name="image_url" maxlength="500">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="description">الوصف</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">الإعدادات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_active">الحالة</label>
                                        <select class="form-control" id="is_active" name="is_active">
                                            <option value="1">نشط</option>
                                            <option value="0">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sort_order">ترتيب العرض</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" min="0">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-category-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Category Modal -->
<div class="modal fade" id="showCategoryModal" tabindex="-1" role="dialog" aria-labelledby="showCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showCategoryModalLabel">تفاصيل الفئة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Basic Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>اسم الفئة:</strong></label>
                                    <p id="show_name"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>كود الفئة:</strong></label>
                                    <p id="show_code"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>القائمة:</strong></label>
                                    <p id="show_menu"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الفئة الأب:</strong></label>
                                    <p id="show_parent_category"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الأيقونة:</strong></label>
                                    <p id="show_icon"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>رابط الصورة:</strong></label>
                                    <p id="show_image_url"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><strong>الوصف:</strong></label>
                                    <p id="show_description"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings & Status -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">الإعدادات والحالة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الحالة:</strong></label>
                                    <p id="show_status"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>ترتيب العرض:</strong></label>
                                    <p id="show_sort_order"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ الإنشاء:</strong></label>
                                    <p id="show_created_at"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ التحديث:</strong></label>
                                    <p id="show_updated_at"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#categoryModal')
    });

    // Load menus for select dropdown
    function loadMenus() {
        $.ajax({
            url: '{{ route("menus.index") }}',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر القائمة</option>';
                $.each(response.data, function(index, menu) {
                    options += '<option value="' + menu.id + '">' + menu.name + '</option>';
                });
                $('#menu_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading menus:', xhr);
            }
        });
    }

    // Load parent categories for select dropdown
    function loadParentCategories() {
        $.ajax({
            url: '{{ route("categories.index") }}',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر الفئة الأب</option>';
                $.each(response.data, function(index, category) {
                    options += '<option value="' + category.id + '">' + category.name + '</option>';
                });
                $('#parent_category_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading parent categories:', xhr);
            }
        });
    }

    // Load menus on page load
    loadMenus();
    loadParentCategories();

    // Initialize DataTable with server-side processing
    var table = $('#categories-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("categories.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'name_en', name: 'name_en', defaultContent: '-' },
            { data: 'menu_name', name: 'menu.name' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'sort_order', name: 'sort_order' },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Add Category Button
    $('#add-category-btn').click(function() {
        $('#categoryForm')[0].reset();
        $('#category_id').val('');
        $('#categoryModalLabel').text('إضافة فئة جديدة');
        $('.form-control').removeClass('is-invalid');
        $('.select2').trigger('change');
        $('#categoryModal').modal('show');
    });

    // Edit Category Button
    $(document).on('click', '.edit-category', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("categories.edit", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                $('#category_id').val(response.id);
                $('#name').val(response.name);
                $('#code').val(response.code);
                $('#menu_id').val(response.menu_id).trigger('change');
                $('#parent_category_id').val(response.parent_category_id).trigger('change');
                $('#icon').val(response.icon);
                $('#image_url').val(response.image_url);
                $('#description').val(response.description);
                $('#is_active').val(response.is_active);
                $('#sort_order').val(response.sort_order);
                $('#categoryModalLabel').text('تعديل الفئة');
                $('.form-control').removeClass('is-invalid');
                $('#categoryModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الفئة', 'error');
            }
        });
    });

    // Show Category Button
    $(document).on('click', '.show-category', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("categories.show", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var category = response;
                $('#show_name').text(category.name || '-');
                $('#show_code').text(category.code || '-');
                $('#show_menu').text(category.menu ? category.menu.name : '-');
                $('#show_parent_category').text(category.parent_category ? category.parent_category.name : '-');
                $('#show_icon').text(category.icon || '-');
                $('#show_image_url').text(category.image_url || '-');
                $('#show_description').text(category.description || '-');
                $('#show_status').html(category.is_active == 1 ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                $('#show_sort_order').text(category.sort_order || '-');
                $('#show_created_at').text(new Date(category.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(category.updated_at).toLocaleDateString('ar-SA'));
                $('#showCategoryModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات الفئة', 'error');
            }
        });
    });

    // Delete Category Button
    $(document).on('click', '.delete-category', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '{{ route("categories.destroy", ":id") }}'.replace(':id', id),
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف الفئة بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف الفئة", "error");
                }
            });
        });
    });

    // Save Category Form
    $('#categoryForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#category_id').val();
        var url = id ? '{{ route("categories.update", ":id") }}'.replace(':id', id) : '{{ route("categories.store") }}';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#categoryModal').modal('hide');
                swal("نجح!", id ? "تم تحديث الفئة بنجاح" : "تم إضافة الفئة بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ الفئة", "error");
                }
            }
        });
    });
});
</script>
@endsection