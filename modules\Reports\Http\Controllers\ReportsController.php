<?php

namespace Modules\Reports\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Report;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Reports\Http\Requests\GenerateReportRequest;
use Modules\Reports\Services\ReportsService;

class ReportsController extends Controller
{
    protected ReportsService $reportsService;

    public function __construct(ReportsService $reportsService)
    {
        $this->reportsService = $reportsService;
    }

    /**
     * Get list of reports for the tenant/branch
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $type = $request->input('type');
            $status = $request->input('status');

            $query = Report::where('tenant_id', $tenant->id);

            if ($branchId) {
                $query->where('branch_id', $branchId);
            }

            if ($type) {
                $query->byType($type);
            }

            if ($status) {
                $query->byStatus($status);
            }

            $reports = $query->orderBy('created_at', 'desc')
                           ->paginate($request->input('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $reports,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reports',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate a new report
     */
    public function generate(GenerateReportRequest $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $data = $request->validated();
            $data['tenant_id'] = $tenant->id;
            $data['generated_by'] = $request->user()->id ?? 1;

            $report = $this->reportsService->generateReport($data);

            return response()->json([
                'success' => true,
                'data' => $report,
                'message' => 'Report generation started',
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show a specific report
     */
    public function show(Request $request, Report $report): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');

            // Ensure the report belongs to the current tenant
            if ($report->tenant_id !== $tenant->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download a report file
     */
    public function download(Request $request, Report $report)
    {
        try {
            $tenant = $request->get('tenant');

            // Ensure the report belongs to the current tenant
            if ($report->tenant_id !== $tenant->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report not found',
                ], 404);
            }

            $format = $request->get('format', $report->format ?? 'pdf');

            // Update report format if different
            if ($report->format !== $format) {
                $report->update(['format' => $format]);
            }

            $filePath = $this->reportsService->exportReport($report, $format);

            // Update report with file path
            $report->update([
                'file_path' => $filePath,
                'status' => 'completed'
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'download_url' => $filePath,
                    'format' => $format,
                    'report' => $report->fresh()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a report
     */
    public function destroy(Request $request, Report $report): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');

            // Ensure the report belongs to the current tenant
            if ($report->tenant_id !== $tenant->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report not found',
                ], 404);
            }

            $this->reportsService->deleteReport($report);

            return response()->json([
                'success' => true,
                'message' => 'Report deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Daily Reports Methods
    public function dailySalesSummary(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $date = $request->input('date', now()->toDateString());

            $report = $this->reportsService->generateDailySalesSummary($tenant->id, $branchId, $date);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate daily sales summary',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dailyStockDrawer(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $date = $request->input('date', now()->toDateString());

            $report = $this->reportsService->generateDailyStockDrawer($tenant->id, $branchId, $date);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate daily stock drawer report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dailyPayments(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $date = $request->input('date', now()->toDateString());

            $report = $this->reportsService->generateDailyPayments($tenant->id, $branchId, $date);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate daily payments report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dailyDiscounts(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $date = $request->input('date', now()->toDateString());

            $report = $this->reportsService->generateDailyDiscounts($tenant->id, $branchId, $date);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate daily discounts report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dailyReturns(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $date = $request->input('date', now()->toDateString());

            $report = $this->reportsService->generateDailyReturns($tenant->id, $branchId, $date);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate daily returns report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function dailyCashDrawer(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $date = $request->input('date', now()->toDateString());

            $report = $this->reportsService->generateDailyCashDrawer($tenant->id, $branchId, $date);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate daily cash drawer report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Periodic Reports Methods
    public function productPerformance(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateProductPerformance($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate product performance report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function staffPerformance(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateStaffPerformance($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate staff performance report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function profitReport(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateProfitReport($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate profit report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function customerReport(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateCustomerReport($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate customer report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Inventory Reports Methods
    public function stockMovement(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateStockMovement($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate stock movement report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function reorderPoint(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');

            $report = $this->reportsService->generateReorderPoint($tenant->id, $branchId);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate reorder point report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Restaurant-Specific Reports Methods
    public function menuItemsReport(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateMenuItemsReport($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate menu items report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function mealTimeSales(Request $request): JsonResponse
    {
        try {
            $tenant = $request->get('tenant');
            $branchId = $request->input('branch_id');
            $period = $request->input('period', 'current_month');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $report = $this->reportsService->generateMealTimeSales($tenant->id, $branchId, $period, $startDate, $endDate);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate meal time sales report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
