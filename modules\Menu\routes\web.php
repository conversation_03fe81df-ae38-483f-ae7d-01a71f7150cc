<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\Web\MenuController;
use Modules\Menu\Http\Controllers\Web\CategoryController;
use Modules\Menu\Http\Controllers\Web\MenuItemController;
use Modules\Menu\Http\Controllers\Web\AddonController;
use Modules\Menu\Http\Controllers\Web\VariantController;

/*
|--------------------------------------------------------------------------
| Menu Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Menu module.
| All routes are prefixed with 'menu' and require authentication.
|
*/

Route::prefix('menu')->middleware(['auth'])->group(function () {
    
    // Menu Routes
    Route::get('/menus', [MenuController::class, 'index'])->name('menus.index');
    Route::post('/menus', [MenuController::class, 'store'])->name('menus.store');
    Route::get('/menus/{id}/show', [MenuController::class, 'show'])->name('menus.show');
    Route::get('/menus/{id}/edit', [MenuController::class, 'edit'])->name('menus.edit');
    Route::put('/menus/{id}', [MenuController::class, 'update'])->name('menus.update');
    Route::delete('/menus/{id}', [MenuController::class, 'destroy'])->name('menus.destroy');
    Route::get('/menus/list', [MenuController::class, 'getMenusList'])->name('menus.list');
    
    // Category Routes
    Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::post('/categories', [CategoryController::class, 'store'])->name('categories.store');
    Route::get('/categories/{id}/show', [CategoryController::class, 'show'])->name('categories.show');
    Route::get('/categories/{id}/edit', [CategoryController::class, 'edit'])->name('categories.edit');
    Route::put('/categories/{id}', [CategoryController::class, 'update'])->name('categories.update');
    Route::delete('/categories/{id}', [CategoryController::class, 'destroy'])->name('categories.destroy');
    Route::get('/categories/list', [CategoryController::class, 'getCategoriesList'])->name('categories.list');
    
    // Menu Item Routes
    Route::get('/menu-items', [MenuItemController::class, 'index'])->name('menu-items.index');
    Route::post('/menu-items', [MenuItemController::class, 'store'])->name('menu-items.store');
    Route::get('/menu-items/{id}/show', [MenuItemController::class, 'show'])->name('menu-items.show');
    Route::get('/menu-items/{id}/edit', [MenuItemController::class, 'edit'])->name('menu-items.edit');
    Route::put('/menu-items/{id}', [MenuItemController::class, 'update'])->name('menu-items.update');
    Route::delete('/menu-items/{id}', [MenuItemController::class, 'destroy'])->name('menu-items.destroy');
    Route::get('/menu-items/list', [MenuItemController::class, 'getMenuItemsList'])->name('menu-items.list');
    
    // Addon Routes
    Route::get('/addons', [AddonController::class, 'index'])->name('addons.index');
    Route::post('/addons', [AddonController::class, 'store'])->name('addons.store');
    Route::get('/addons/{id}/show', [AddonController::class, 'show'])->name('addons.show');
    Route::get('/addons/{id}/edit', [AddonController::class, 'edit'])->name('addons.edit');
    Route::put('/addons/{id}', [AddonController::class, 'update'])->name('addons.update');
    Route::delete('/addons/{id}', [AddonController::class, 'destroy'])->name('addons.destroy');
    
    // Variant Routes
    Route::get('/variations', [VariantController::class, 'index'])->name('variations.index');
    Route::post('/variations', [VariantController::class, 'store'])->name('variations.store');
    Route::get('/variations/{id}/show', [VariantController::class, 'show'])->name('variations.show');
    Route::get('/variations/{id}/edit', [VariantController::class, 'edit'])->name('variations.edit');
    Route::put('/variations/{id}', [VariantController::class, 'update'])->name('variations.update');
    Route::delete('/variations/{id}', [VariantController::class, 'destroy'])->name('variations.destroy');
    
});
