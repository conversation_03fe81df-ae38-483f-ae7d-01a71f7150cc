<?php

use Illuminate\Support\Facades\Route;
use Modules\Reports\Http\Controllers\ReportsWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth', 'tenant'])->prefix('reports')->name('reports.')->group(function () {
    // Reports dashboard
    Route::get('/', [ReportsWebController::class, 'index'])->name('index');

    // Report generation form
    Route::get('/generate', [ReportsWebController::class, 'create'])->name('create');
    Route::post('/generate', [ReportsWebController::class, 'store'])->name('store');

    // View specific report
    Route::get('/{report}', [ReportsWebController::class, 'show'])->name('show');

    // Download report
    Route::get('/{report}/download', [ReportsWebController::class, 'download'])->name('download');

    // Delete report
    Route::delete('/{report}', [ReportsWebController::class, 'destroy'])->name('destroy');

    // Quick report generation routes
    Route::get('/quick/daily-sales', [ReportsWebController::class, 'quickDailySales'])->name('quick.daily-sales');
    Route::get('/quick/hourly-sales', [ReportsWebController::class, 'quickHourlySales'])->name('quick.hourly-sales');
    Route::get('/quick/tax-report', [ReportsWebController::class, 'quickTaxReport'])->name('quick.tax-report');
    Route::get('/quick/void-cancelled', [ReportsWebController::class, 'quickVoidCancelled'])->name('quick.void-cancelled');

    // Category-specific routes
    Route::prefix('category')->name('category.')->group(function () {
        Route::get('/daily', [ReportsWebController::class, 'dailyReports'])->name('daily');
        Route::get('/periodic', [ReportsWebController::class, 'periodicReports'])->name('periodic');
        Route::get('/advanced', [ReportsWebController::class, 'advancedReports'])->name('advanced');
        Route::get('/restaurant', [ReportsWebController::class, 'restaurantReports'])->name('restaurant');
    });
});
