<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="Description" content="Bootstrap Responsive Admin Web Dashboard HTML5 Template">
		<meta name="Author" content="Spruko Technologies Private Limited">
		<meta name="Keywords" content="admin,admin dashboard,admin dashboard template,admin panel template,admin template,admin theme,bootstrap 4 admin template,bootstrap 4 dashboard,bootstrap admin,bootstrap admin dashboard,bootstrap admin panel,bootstrap admin template,bootstrap admin theme,bootstrap dashboard,bootstrap form template,bootstrap panel,bootstrap ui kit,dashboard bootstrap 4,dashboard design,dashboard html,dashboard template,dashboard ui kit,envato templates,flat ui,html,html and css templates,html dashboard template,html5,jquery html,premium,premium quality,sidebar bootstrap 4,template admin bootstrap 4"/>
		<?php echo $__env->make('layouts.head', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
		
	</head>

	<body class="main-body app sidebar-mini">
		<!-- Loader -->
		<div id="global-loader">
			<img src="<?php echo e(URL::asset('assets/img/loader.svg')); ?>" class="loader-img" alt="Loader">
		</div>
		<!-- /Loader -->
		<?php echo $__env->make('layouts.main-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>		
		<!-- main-content -->
		<div class="main-content app-content">
			<?php echo $__env->make('layouts.main-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>			
			<!-- container -->
			<div class="container-fluid">
				<?php echo $__env->yieldContent('page-header'); ?>
				<?php echo $__env->yieldContent('content'); ?>
				<?php echo $__env->make('layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
				<?php echo $__env->make('layouts.models', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            	<!-- <?php echo $__env->make('layouts.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?> -->
				<?php echo $__env->make('layouts.footer-scripts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
				<?php echo $__env->yieldPushContent('scripts'); ?>
		
				
				<!-- Flash Messages -->
				<?php echo $__env->make('layouts.flash-messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
				
				<!-- Toast Container -->
				<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
	</body>
</html><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\resources\views/layouts/master.blade.php ENDPATH**/ ?>