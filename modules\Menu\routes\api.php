<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\Api\Sanctum\MenuController;
use Modules\Menu\Http\Controllers\Api\Sanctum\CategoryController;
use Modules\Menu\Http\Controllers\Api\Sanctum\MenuItemController;
use Modules\Menu\Http\Controllers\Api\Sanctum\AddonController;
use Modules\Menu\Http\Controllers\Api\Sanctum\VariantController;
use Modules\Menu\Http\Controllers\Api\Public\PublicMenuController;

/*
|--------------------------------------------------------------------------
| Menu Module API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Menu module.
| All routes are prefixed with 'api/menu' and require authentication.
|
*/

Route::prefix('api/menu')->middleware(['auth:sanctum'])->name('api.menu.')->group(function () {
    
    // Menu routes
    Route::apiResource('menus', MenuController::class);
    
    // Category routes
    Route::apiResource('categories', CategoryController::class);
    
    // Menu Item routes
    Route::apiResource('menu-items', MenuItemController::class);
    
    // Addon routes
    Route::apiResource('addons', AddonController::class);
    
    // Variant routes
    Route::apiResource('variants', VariantController::class);
    
});

// Public routes (no authentication required)
Route::prefix('menu/public')->name('api.menu.public.')->group(function () {
    
    // Public menu endpoints for customer-facing applications
    Route::get('menus', [MenuController::class, 'index'])->name('menus.index');
    Route::get('menus/{id}', [MenuController::class, 'show'])->name('menus.show');

});

// Public routes with restaurant (tenant) name parameter
Route::prefix('menu/restaurant/{tenantName}')->name('api.menu.restaurant.')->group(function () {
    
    // Restaurant info
    Route::get('info', [PublicMenuController::class, 'getRestaurantInfo'])->name('info');
    
    // Menus
    Route::get('menus', [PublicMenuController::class, 'getMenus'])->name('menus.index');
    Route::get('menus/{menuId}', [PublicMenuController::class, 'getMenu'])->name('menus.show');
    
    // Categories (will be implemented)
    Route::get('categories', [PublicMenuController::class, 'getCategories'])->name('categories.index');
    // Route::get('categories/{categoryId}', [PublicMenuController::class, 'getCategory'])->name('categories.show');
    
    // Menu Items (will be implemented)
    Route::get('menu-items', [PublicMenuController::class, 'getMenuItems'])->name('menu-items.index');
    // Route::get('menu-items/{itemId}', [PublicMenuController::class, 'getMenuItem'])->name('menu-items.show');
    
    // Addons (will be implemented)
    Route::get('addons', [PublicMenuController::class, 'getAddons'])->name('addons.index');
    
    // Variants (will be implemented)
    Route::get('variants', [PublicMenuController::class, 'getVariants'])->name('variants.index');
    
});