<?php

namespace Modules\Menu\Http\Controllers\Web;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;use Modules\Menu\Http\Requests\StoreAddonRequest;
use Modules\Menu\Http\Requests\UpdateAddonRequest;
use Modules\Menu\Services\AddonService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class AddonController extends Controller
{
    protected $addonService;

    public function __construct(AddonService $addonService)
    {
        $this->addonService = $addonService;
    }

    /**
     * Display a listing of addons for DataTable
     */
    public function index(Request $request)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            if ($request->ajax()) {
                return $this->addonService->getAddonsForDataTable($branchId, $request);
            }

            return view('menu::addons');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addons: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created addon
     */
    public function store(StoreAddonRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $addon = $this->addonService->createAddonForWeb($data);
            
            return ResponseHelper::success('Addon created successfully', $addon);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create addon: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified addon
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->getAddonByIdForBranch($id, $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success('Addon retrieved successfully', $addon);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addon: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified addon
     */
    public function edit(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->getAddonByIdForBranch($id, $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success('Addon retrieved for editing', $addon);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addon: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified addon
     */
    public function update(UpdateAddonRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->updateAddonForWeb($id, $request->validated(), $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success('Addon updated successfully', $addon);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update addon: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified addon
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->addonService->deleteAddonForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success('Addon deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete addon: ' . $e->getMessage());
        }
    }
}