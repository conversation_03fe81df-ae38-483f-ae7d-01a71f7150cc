<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAddonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'addon_group_name' => 'nullable|string|max:100',
            'name' => 'sometimes|string|max:100',
            'code' => 'sometimes|string|max:50',
            'price' => 'sometimes|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
            'is_required' => 'boolean',
            'max_quantity' => 'integer|min:1',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'addon_group_name.string' => 'اسم مجموعة الإضافة يجب أن يكون نص',
            'addon_group_name.max' => 'اسم مجموعة الإضافة يجب ألا يتجاوز 100 حرف',
            'name.string' => 'اسم الإضافة يجب أن يكون نص',
            'name.max' => 'اسم الإضافة يجب ألا يتجاوز 100 حرف',
            'code.string' => 'كود الإضافة يجب أن يكون نص',
            'code.max' => 'كود الإضافة يجب ألا يتجاوز 50 حرف',
            'price.numeric' => 'سعر الإضافة يجب أن يكون رقم',
            'price.min' => 'سعر الإضافة يجب أن يكون على الأقل 0',
            'cost.numeric' => 'تكلفة الإضافة يجب أن تكون رقم',
            'cost.min' => 'تكلفة الإضافة يجب أن تكون على الأقل 0',
            'is_required.boolean' => 'حالة الإجبارية يجب أن تكون صحيح أو خطأ',
            'max_quantity.integer' => 'الحد الأقصى للكمية يجب أن يكون رقم صحيح',
            'max_quantity.min' => 'الحد الأقصى للكمية يجب أن يكون على الأقل 1',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}