@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة المتغيرات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ المتغيرات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-variation-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة المتغيرات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع المتغيرات المتاحة</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="variations-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم المتغير</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">السعر الإضافي</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Variation Modal -->
<div class="modal fade" id="variationModal" tabindex="-1" role="dialog" aria-labelledby="variationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variationModalLabel">إضافة متغير جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="variationForm">
                <div class="modal-body">
                    <input type="hidden" id="variation_id" name="variation_id">
                    
                    <!-- Basic Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="menu_item_id">عنصر القائمة <span class="text-danger">*</span></label>
                                        <select class="form-control select2" id="menu_item_id" name="menu_item_id" required>
                                            <option value="">اختر عنصر القائمة</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">اسم التنويع <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" required maxlength="100">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code">كود التنويع <small class="text-muted">(يتم إنشاؤه تلقائياً)</small></label>
                                        <input type="text" class="form-control" id="code" name="code" placeholder="سيتم إنشاؤه تلقائياً من الاسم" readonly maxlength="50">
                                        <small class="form-text text-muted">سيتم إنشاء الكود تلقائياً بناءً على اسم التنويع</small>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_default">افتراضي</label>
                                        <select class="form-control" id="is_default" name="is_default">
                                            <option value="0">لا</option>
                                            <option value="1">نعم</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات التسعير</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price_modifier">معدل السعر <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control" id="price_modifier" name="price_modifier" required>
                                        <small class="form-text text-muted">القيمة المضافة أو المخصومة من السعر الأساسي</small>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cost_modifier">معدل التكلفة</label>
                                        <input type="number" step="0.01" class="form-control" id="cost_modifier" name="cost_modifier">
                                        <small class="form-text text-muted">القيمة المضافة أو المخصومة من التكلفة الأساسية</small>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">الإعدادات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sort_order">ترتيب العرض</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" min="0">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-variation-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Variation Modal -->
<div class="modal fade" id="showVariationModal" tabindex="-1" role="dialog" aria-labelledby="showVariationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showVariationModalLabel">تفاصيل التنويع</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Basic Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>عنصر القائمة:</strong></label>
                                    <p id="show_menu_item"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>اسم التنويع:</strong></label>
                                    <p id="show_name"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>كود التنويع:</strong></label>
                                    <p id="show_code"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>افتراضي:</strong></label>
                                    <p id="show_is_default"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">معلومات التسعير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>معدل السعر:</strong></label>
                                    <p id="show_price_modifier"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>معدل التكلفة:</strong></label>
                                    <p id="show_cost_modifier"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings & Status -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">الإعدادات والحالة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>ترتيب العرض:</strong></label>
                                    <p id="show_sort_order"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ الإنشاء:</strong></label>
                                    <p id="show_created_at"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ التحديث:</strong></label>
                                    <p id="show_updated_at"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize DataTable with server-side processing
    var table = $('#variations-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("variations.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'type', name: 'type' },
            { data: 'price', name: 'price' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Load menu items for dropdown
    function loadMenuItems() {
        $.ajax({
            url: '{{ route("menu-items.list") }}',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر عنصر القائمة</option>';
                $.each(response, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.name + ' (' + item.price + ' ريال)</option>';
                });
                $('#menu_item_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading menu items:', xhr);
            }
        });
    }

    // Add Variation Button
    $('#add-variation-btn').click(function() {
        $('#variationForm')[0].reset();
        $('#variation_id').val('');
        $('#variationModalLabel').text('إضافة تنويع جديد');
        $('.form-control').removeClass('is-invalid');
        $('#code').val(''); // Clear code field for new variations
        loadMenuItems(); // Load menu items when opening modal
        $('#variationModal').modal('show');
    });

    // Generate code preview when name changes
    $('#name').on('input', function() {
        var name = $(this).val();
        if (name && !$('#variation_id').val()) { // Only for new variations
            var code = generateCodeFromName(name, 'VAR');
            $('#code').val(code);
        }
    });

    // Function to generate code from name
    function generateCodeFromName(name, prefix) {
        // Remove Arabic diacritics and special characters
        var cleanName = name.replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, '');
        
        // Map common Arabic words to English
        var arabicToEnglish = {
            'كبير': 'LARGE',
            'صغير': 'SMALL',
            'متوسط': 'MEDIUM',
            'عادي': 'REGULAR',
            'مميز': 'PREMIUM',
            'حار': 'SPICY',
            'معتدل': 'MILD',
            'بارد': 'COLD',
            'ساخن': 'HOT',
            'مقلي': 'FRIED',
            'مشوي': 'GRILLED',
            'مسلوق': 'BOILED',
            'نيء': 'RAW',
            'مطبوخ': 'COOKED'
        };
        
        var words = cleanName.split(' ');
        var codeWords = [];
        
        words.forEach(function(word) {
            word = word.trim();
            if (word) {
                if (arabicToEnglish[word]) {
                    codeWords.push(arabicToEnglish[word]);
                } else {
                    // Take first 3 characters and convert to uppercase
                    codeWords.push(word.substring(0, 3).toUpperCase());
                }
            }
        });
        
        var baseCode = codeWords.join('_');
        return prefix + '_' + baseCode;
    }

    // Edit Variation Button
    $(document).on('click', '.edit-variation', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("variations.edit", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                loadMenuItems(); // Load menu items first
                setTimeout(function() {
                    $('#variation_id').val(response.id);
                    $('#menu_item_id').val(response.menu_item_id);
                    $('#name').val(response.name);
                    $('#code').val(response.code);
                    $('#price_modifier').val(response.price_modifier);
                    $('#cost_modifier').val(response.cost_modifier);
                    $('#is_default').val(response.is_default);
                    $('#sort_order').val(response.sort_order);
                    $('#variationModalLabel').text('تعديل التنويع');
                    $('.form-control').removeClass('is-invalid');
                    $('#variationModal').modal('show');
                }, 500); // Wait for menu items to load
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات التنويع', 'error');
            }
        });
    });

    // Show Variation Button
    $(document).on('click', '.show-variation', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("variations.show", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var variation = response;
                
                // Populate menu item name
                $('#show_menu_item').text(variation.menu_item ? variation.menu_item.name : 'غير محدد');
                $('#show_name').text(variation.name || '-');
                $('#show_code').text(variation.code || '-');
                $('#show_is_default').html(variation.is_default == 1 ? '<span class="badge badge-primary">افتراضي</span>' : '<span class="badge badge-secondary">غير افتراضي</span>');
                $('#show_price_modifier').text(variation.price_modifier ? variation.price_modifier + ' ريال' : '0 ريال');
                $('#show_cost_modifier').text(variation.cost_modifier ? variation.cost_modifier + ' ريال' : '0 ريال');
                $('#show_sort_order').text(variation.sort_order || '-');
                $('#show_created_at').text(new Date(variation.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(variation.updated_at).toLocaleDateString('ar-SA'));
                $('#showVariationModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات التنويع', 'error');
            }
        });
    });

    // Delete Variation Button
    $(document).on('click', '.delete-variation', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '{{ route("variations.destroy", ":id") }}'.replace(':id', id),
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف التنويع بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف التنويع", "error");
                }
            });
        });
    });

    // Save Variation Form
    $('#variationForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#variation_id').val();
        var url = id ? '{{ route("variations.update", ":id") }}'.replace(':id', id) : '{{ route("variations.store") }}';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#variationModal').modal('hide');
                swal("نجح!", id ? "تم تحديث التنويع بنجاح" : "تم إضافة التنويع بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ التنويع", "error");
                }
            }
        });
    });
});
</script>
@endsection