<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\EventService;
use Modules\Menu\Http\Requests\StoreEventRequest;
use Modules\Menu\Http\Requests\UpdateEventRequest;
use Modules\Menu\Models\Event;

/**
 * Event Controller
 * 
 * Handles HTTP requests for event management in the restaurant system.
 * Provides endpoints for CRUD operations, event registration, analytics,
 * and event status management.
 * 
 * @package Modules\Menu\Http\Controllers
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class EventController extends Controller
{
    protected EventService $eventService;

    public function __construct(EventService $eventService)
    {
        $this->eventService = $eventService;
    }

    /**
     * Display a listing of events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $filters = $request->only([
                    'branch_id', 'event_type', 'is_active', 'is_featured', 
                    'start_date', 'end_date', 'search', 'sort_by', 'sort_order'
                ]);

                $perPage = $request->get('per_page', 15);
                $events = $this->eventService->getAllEvents($filters, $perPage);

                return response()->json([
                    'success' => true,
                    'message' => 'Events retrieved successfully',
                    'data' => $events
                ]);
            }

            return view('menu::events');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created event
     *
     * @param StoreEventRequest $request
     * @return JsonResponse
     */
    public function store(StoreEventRequest $request): JsonResponse
    {
        try {
            $event = $this->eventService->createEvent($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Event created successfully',
                'data' => $event
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified event
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $event = $this->eventService->getEventById($id);

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Event retrieved successfully',
                'data' => $event
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified event
     *
     * @param UpdateEventRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateEventRequest $request, int $id): JsonResponse
    {
        try {
            $event = $this->eventService->updateEvent($id, $request->validated());

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Event updated successfully',
                'data' => $event
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified event
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->eventService->deleteEvent($id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Event deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get featured events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $events = $this->eventService->getFeaturedEvents($branchId, $limit);

            return response()->json([
                'success' => true,
                'message' => 'Featured events retrieved successfully',
                'data' => $events
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve featured events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get upcoming events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function upcoming(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $events = $this->eventService->getUpcomingEvents($branchId, $limit);

            return response()->json([
                'success' => true,
                'message' => 'Upcoming events retrieved successfully',
                'data' => $events
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve upcoming events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register for an event
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function register(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'customer_id' => 'required|integer',
                'participants' => 'required|integer|min:1',
                'contact_info' => 'nullable|array',
                'special_requests' => 'nullable|string'
            ]);

            $registration = $this->eventService->registerForEvent(
                $id,
                $request->get('customer_id'),
                $request->get('participants'),
                $request->get('contact_info', []),
                $request->get('special_requests')
            );

            if (!$registration) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to register for event. Event may be full or not available.'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Successfully registered for event',
                'data' => $registration
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to register for event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get event analytics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function analytics(int $id): JsonResponse
    {
        try {
            $analytics = $this->eventService->getEventAnalytics($id);

            if (!$analytics) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Event analytics retrieved successfully',
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve event analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle event status
     *
     * @param int $id
     * @return JsonResponse
     */
    public function toggleStatus(int $id): JsonResponse
    {
        try {
            $event = $this->eventService->toggleEventStatus($id);

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Event status updated successfully',
                'data' => $event
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update event status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate an event
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function duplicate(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'required|string|max:50|unique:events,code',
                'start_date' => 'required|date|after_or_equal:today',
                'end_date' => 'required|date|after_or_equal:start_date'
            ]);

            $duplicatedEvent = $this->eventService->duplicateEvent($id, [
                'name' => $request->get('name'),
                'code' => $request->get('code'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date')
            ]);

            if (!$duplicatedEvent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Event duplicated successfully',
                'data' => $duplicatedEvent
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get events by type
     *
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     */
    public function byType(Request $request, string $type): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $events = Event::forBranch($branchId)
                ->active()
                ->byType($type)
                ->orderBy('start_date', 'asc')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'message' => "Events of type '{$type}' retrieved successfully",
                'data' => $events
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve events by type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current events (happening now)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function current(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            
            $events = Event::forBranch($branchId)
                ->active()
                ->current()
                ->orderBy('start_date', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Current events retrieved successfully',
                'data' => $events
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve current events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get event calendar data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calendar(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'branch_id' => 'nullable|integer|exists:branches,id'
            ]);

            $branchId = $request->get('branch_id');
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');

            $events = Event::forBranch($branchId)
                ->active()
                ->whereBetween('start_date', [$startDate, $endDate])
                ->orderBy('start_date', 'asc')
                ->get()
                ->map(function ($event) {
                    return [
                        'id' => $event->id,
                        'title' => $event->name,
                        'start' => $event->start_date->format('Y-m-d') . 'T' . ($event->start_time ?? '00:00:00'),
                        'end' => $event->end_date->format('Y-m-d') . 'T' . ($event->end_time ?? '23:59:59'),
                        'type' => $event->event_type,
                        'description' => $event->description,
                        'location' => $event->location,
                        'price' => $event->price,
                        'available_spots' => $event->getAvailableSpots(),
                        'is_featured' => $event->is_featured,
                        'requires_reservation' => $event->requires_reservation
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'Event calendar data retrieved successfully',
                'data' => $events
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve event calendar data',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}