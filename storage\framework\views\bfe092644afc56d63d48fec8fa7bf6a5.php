<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Episys POS Login</title>
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
  <script src="https://unpkg.com/feather-icons"></script>
  <style>
    .glass-effect {
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .floating-animation {
      animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    
    .shimmer {
      position: relative;
      overflow: hidden;
    }
    
    .shimmer::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }
    
    .shimmer:hover::before {
      left: 100%;
    }
    
    .input-focus {
      transition: all 0.3s ease;
    }
    
    .input-focus:focus-within {
      border-color: #0162e8;
      box-shadow: 0 0 0 3px rgba(1, 98, 232, 0.1);
      transform: translateY(-2px);
    }
    
    .pulse-ring {
      animation: pulse-ring 2s infinite;
    }
    
    @keyframes pulse-ring {
      0% { box-shadow: 0 0 0 0 rgba(1, 98, 232, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(1, 98, 232, 0); }
      100% { box-shadow: 0 0 0 0 rgba(1, 98, 232, 0); }
    }
    
    .stagger-animation > * {
      animation-delay: calc(var(--i) * 100ms);
    }
    
    .brand-pattern {
      background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    }
  </style>
</head>
<body class="h-full bg-gray-50 font-sans antialiased">
  <div class="flex h-screen">
    <!-- Left Side - Enhanced Branded Section -->
    <div class="w-1/2 hidden lg:flex items-center justify-center bg-gradient-to-br from-[#0162e8] via-[#0152d8] to-[#0142c8] text-white animate__animated animate__fadeInLeft relative overflow-hidden brand-pattern">
      <!-- Background decorative elements -->
      <div class="absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full animate-pulse"></div>
      <div class="absolute bottom-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
      

      
      <div class="text-center space-y-8 max-w-md px-8 z-10">
        <!-- Enhanced logo with floating animation -->
        <div class="flex items-center justify-center floating-animation">
          <div class="glass-effect p-6 rounded-2xl shadow-2xl">
            <i data-feather="coffee" class="text-white w-12 h-12"></i>
          </div>
        </div>
        
        <div class="space-y-4">
          <h1 class="text-5xl font-black tracking-tight">Episys</h1>
          <p class="text-xl leading-relaxed opacity-90">
            The smart way to manage orders, tables, and sales. 
            <span class="block mt-2 text-lg font-semibold">Fast. Simple. Reliable.</span>
          </p>
        </div>
        
        <!-- Feature highlights -->
        <div class="grid grid-cols-1 gap-3 mt-8">
          <div class="flex items-center gap-3 text-sm opacity-80">
            <i data-feather="zap" class="w-4 h-4"></i>
            <span>Lightning-fast order processing</span>
          </div>
          <div class="flex items-center gap-3 text-sm opacity-80">
            <i data-feather="shield" class="w-4 h-4"></i>
            <span>Enterprise-grade security</span>
          </div>
          <div class="flex items-center gap-3 text-sm opacity-80">
            <i data-feather="trending-up" class="w-4 h-4"></i>
            <span>Real-time analytics</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right Side - Enhanced Login Form -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-white animate__animated animate__fadeInRight relative">
      <!-- Mobile logo for when left side is hidden -->
      <div class="lg:hidden absolute top-6 left-6">
        <div class="flex items-center gap-2">
          <div class="bg-gradient-to-r from-[#0162e8] to-[#0142c8] p-2 rounded-lg">
            <i data-feather="coffee" class="text-white w-6 h-6"></i>
          </div>
          <span class="text-xl font-bold text-gray-800">Episys</span>
        </div>
      </div>
      
      <div class="w-full max-w-md space-y-8">
        <!-- Enhanced header -->
        <div class="text-center space-y-3">
          <h2 class="text-3xl font-bold text-gray-900">Welcome Back</h2>
          <p class="text-gray-600">Sign in to access your POS dashboard</p>
        </div>
        
        <!-- Error message placeholder -->
        <?php if($errors->any()): ?>
        <div class="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
          <div class="flex items-center gap-2">
            <i data-feather="alert-circle" class="w-4 h-4"></i>
            <span><?php echo e($errors->first()); ?></span>
          </div>
        </div>
        <?php endif; ?>
        
        <!-- Enhanced form with staggered animations -->
        <form class="space-y-6 stagger-animation" method="POST" action="<?php echo e(route('login')); ?>">
          <?php echo csrf_field(); ?>
          <div class="animate__animated animate__fadeInUp" style="--i: 1;">
            <label class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
            <div class="flex items-center border-2 border-gray-200 rounded-xl px-4 py-3 input-focus">
              <i data-feather="mail" class="w-5 h-5 text-gray-400 mr-3"></i>
              <input 
                type="email" 
                name="email"
                value="<?php echo e(old('email')); ?>"
                placeholder="Enter your email" 
                class="w-full outline-none text-gray-700 placeholder-gray-400" 
                required
                autofocus
              />
            </div>
          </div>
          
          <div class="animate__animated animate__fadeInUp" style="--i: 2;">
            <label class="block text-sm font-semibold text-gray-700 mb-2">Password</label>
            <div class="flex items-center border-2 border-gray-200 rounded-xl px-4 py-3 input-focus">
              <i data-feather="lock" class="w-5 h-5 text-gray-400 mr-3"></i>
              <input 
                type="password" 
                name="password"
                placeholder="Enter your password" 
                class="w-full outline-none text-gray-700 placeholder-gray-400" 
                required
              />
              <button type="button" class="ml-2 text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                <i data-feather="eye" id="toggle-icon" class="w-5 h-5"></i>
              </button>
            </div>
          </div>
          
          <div class="flex justify-between items-center text-sm animate__animated animate__fadeInUp" style="--i: 3;">
            <label class="flex items-center gap-2 cursor-pointer">
              <input type="checkbox" name="remember" class="w-4 h-4 text-[#0162e8] border-gray-300 rounded focus:ring-[#0162e8]" />
              <span class="text-gray-600">Remember me</span>
            </label>
            <a href="<?php echo e(route('password.request')); ?>" class="text-[#0162e8] hover:text-[#0152d8] font-medium hover:underline transition-colors">
              Forgot Password?
            </a>
          </div>
          
          <button 
            type="submit" 
            class="w-full bg-gradient-to-r from-[#0162e8] to-[#0152d8] hover:from-[#0152d8] hover:to-[#0142c8] text-white py-3 px-6 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 shimmer animate__animated animate__fadeInUp"
            style="--i: 4;"
          >
            Sign In to Dashboard
          </button>
        </form>
        
        <!-- Quick access section -->
        <div class="animate__animated animate__fadeInUp" style="--i: 5;">
          <div class="text-center mb-4">
            <span class="text-sm text-gray-500 font-medium">Quick Access</span>
          </div>
          <div class="grid grid-cols-2 gap-3">
            <button class="flex items-center justify-center gap-2 py-2 px-4 border-2 border-gray-200 rounded-lg hover:border-[#0162e8] hover:bg-blue-50 transition-all duration-200 text-sm font-medium text-gray-600 hover:text-[#0162e8]">
              <i data-feather="users" class="w-4 h-4"></i>
              Staff Login
            </button>
            <button class="flex items-center justify-center gap-2 py-2 px-4 border-2 border-gray-200 rounded-lg hover:border-[#0162e8] hover:bg-blue-50 transition-all duration-200 text-sm font-medium text-gray-600 hover:text-[#0162e8]">
              <i data-feather="settings" class="w-4 h-4"></i>
              Manager
            </button>
          </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center text-sm text-gray-500 animate__animated animate__fadeInUp" style="--i: 6;">
          <p>Need help? 
            <a href="#" class="text-[#0162e8] hover:text-[#0152d8] font-medium hover:underline transition-colors">
              Contact Support
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Initialize feather icons
    feather.replace();
    
    // Form submission handler
    document.getElementById('loginForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const errorMessage = document.getElementById('error-message');
      
      // Simple demo validation
      if (email === '<EMAIL>' && password === 'admin123') {
        // Success
        errorMessage.classList.add('hidden');
        
        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i data-feather="loader" class="w-5 h-5 animate-spin inline mr-2"></i>Signing In...';
        submitBtn.disabled = true;
        feather.replace();
        
        setTimeout(() => {
          alert('Login successful! Redirecting to dashboard...');
          submitBtn.innerHTML = originalText;
          submitBtn.disabled = false;
          feather.replace();
        }, 2000);
      } else {
        // Show error
        errorMessage.classList.remove('hidden');
        feather.replace();
        
        // Hide error after 5 seconds
        setTimeout(() => {
          errorMessage.classList.add('hidden');
        }, 5000);
      }
    });
    
    // Password toggle function
    function togglePassword() {
      const passwordInput = document.getElementById('password');
      const toggleIcon = document.getElementById('toggle-icon');
      
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.setAttribute('data-feather', 'eye-off');
      } else {
        passwordInput.type = 'password';
        toggleIcon.setAttribute('data-feather', 'eye');
      }
      
      feather.replace();
    }
    
    // Enhanced input focus effects
    document.querySelectorAll('.input-focus input').forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'translateY(-2px)';
      });
      
      input.addEventListener('blur', function() {
        this.parentElement.style.transform = 'translateY(0)';
      });
    });
  </script>
</body>
</html><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\resources\views/auth/login.blade.php ENDPATH**/ ?>