<?php

namespace Modules\Menu\Http\Controllers\Api\Sanctum;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Http\Requests\StoreVariantRequest;
use Modules\Menu\Http\Requests\UpdateVariantRequest;
use Modules\Menu\Services\VariantService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class VariantController extends Controller
{
    protected $variantService;

    public function __construct(VariantService $variantService)
    {
        $this->variantService = $variantService;
    }

    /**
     * Display a listing of variants
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            // Convert request parameters to filters array
            $filters = $request->only([
                'menu_item_id', 'variant_group_name', 'is_required',
                'min_price', 'max_price', 'search', 'sort_by', 
                'sort_direction', 'per_page'
            ]);
            
            $variants = $this->variantService->getVariantsForBranch($branchId, $filters);
            
            return ResponseHelper::success('Variants retrieved successfully', $variants);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve variants: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created variant
     */
    public function store(StoreVariantRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $variant = $this->variantService->createVariant($data);
            
            return ResponseHelper::success('Variant created successfully', $variant, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create variant: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified variant
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $variant = $this->variantService->getVariantByIdForBranch($id, $branchId);
            
            if (!$variant) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant retrieved successfully', $variant);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve variant: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified variant
     */
    public function update(UpdateVariantRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $variant = $this->variantService->updateVariantForBranch($id, $request->validated(), $branchId);
            
            if (!$variant) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant updated successfully', $variant);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update variant: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified variant
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->variantService->deleteVariantForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete variant: ' . $e->getMessage());
        }
    }
}