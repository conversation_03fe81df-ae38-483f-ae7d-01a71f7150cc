<?php

namespace Modules\Reports\Services;

use App\Models\Order;
use App\Models\Report;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ReportsService
{
    use ReportsServiceExtended;
    /**
     * Generate a report based on the provided data
     */
    public function generateReport(array $data): Report
    {
        $report = Report::create([
            'tenant_id' => $data['tenant_id'],
            'branch_id' => $data['branch_id'] ?? null,
            'name' => $data['name'],
            'type' => $data['type'],
            'parameters' => $data['parameters'] ?? [],
            'format' => $data['format'] ?? 'pdf',
            'status' => 'generating',
            'report_date_from' => $data['start_date'] ?? now()->toDateString(),
            'report_date_to' => $data['end_date'] ?? now()->toDateString(),
            'generated_by' => $data['generated_by'] ?? 1,
        ]);

        // Generate the report data based on type
        $reportData = $this->generateReportData($report);
        
        $report->update([
            'data' => $reportData,
            'status' => 'completed',
            'generated_at' => now(),
        ]);

        return $report;
    }

    /**
     * Generate report data based on report type
     */
    protected function generateReportData(Report $report): array
    {
        $period = $report->parameters['period'] ?? 'today';
        [$startDate, $endDate] = $this->getPeriodDates($period, $report->report_date_from, $report->report_date_to);

        switch ($report->type) {
            case 'sales':
                return $this->generateDailySalesSummary($report->tenant_id, $report->branch_id, $startDate);
            case 'inventory':
                return $this->generateDailyStockDrawer($report->tenant_id, $report->branch_id, $startDate);
            case 'payment':
                return $this->generateDailyPayments($report->tenant_id, $report->branch_id, $startDate);
            case 'discount':
                return $this->generateDailyDiscounts($report->tenant_id, $report->branch_id, $startDate);
            case 'return':
                return $this->generateDailyReturns($report->tenant_id, $report->branch_id, $startDate);
            case 'cash_drawer':
                return $this->generateDailyCashDrawer($report->tenant_id, $report->branch_id, $startDate);
            case 'product_performance':
                return $this->generateProductPerformance($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            case 'staff':
                return $this->generateStaffPerformance($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            case 'profit':
                return $this->generateProfitReport($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            case 'customer':
                return $this->generateCustomerReport($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            case 'stock_movement':
                return $this->generateStockMovement($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            case 'reorder_point':
                return $this->generateReorderPoint($report->tenant_id, $report->branch_id);
            case 'menu_items':
                return $this->generateMenuItemsReport($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            case 'meal_time_sales':
                return $this->generateMealTimeSales($report->tenant_id, $report->branch_id, $period, $startDate, $endDate);
            default:
                throw new \Exception('Invalid report type: ' . $report->type);
        }
    }

    /**
     * Get period dates based on period string
     */
    protected function getPeriodDates(string $period, ?string $customStart = null, ?string $customEnd = null): array
    {
        $now = Carbon::now();

        switch ($period) {
            case 'today':
                return [$now->toDateString(), $now->toDateString()];
            case 'yesterday':
                $yesterday = $now->subDay();
                return [$yesterday->toDateString(), $yesterday->toDateString()];
            case 'current_week':
                return [$now->startOfWeek()->toDateString(), $now->endOfWeek()->toDateString()];
            case 'last_week':
                $lastWeek = $now->subWeek();
                return [$lastWeek->startOfWeek()->toDateString(), $lastWeek->endOfWeek()->toDateString()];
            case 'current_month':
                return [$now->startOfMonth()->toDateString(), $now->endOfMonth()->toDateString()];
            case 'last_month':
                $lastMonth = $now->subMonth();
                return [$lastMonth->startOfMonth()->toDateString(), $lastMonth->endOfMonth()->toDateString()];
            case 'current_year':
                return [$now->startOfYear()->toDateString(), $now->endOfYear()->toDateString()];
            case 'last_year':
                $lastYear = $now->subYear();
                return [$lastYear->startOfYear()->toDateString(), $lastYear->endOfYear()->toDateString()];
            case 'custom':
                return [$customStart ?? $now->toDateString(), $customEnd ?? $now->toDateString()];
            default:
                return [$now->toDateString(), $now->toDateString()];
        }
    }

    /**
     * Get base query for orders filtered by tenant and branch
     */
    protected function getOrdersQuery(int $tenantId, ?int $branchId = null)
    {
        $query = Order::whereHas('branch', function ($q) use ($tenantId) {
            $q->where('tenant_id', $tenantId);
        });

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query;
    }

    /**
     * Download a report file
     */
    public function downloadReport(Report $report)
    {
        if (!$report->file_path || !Storage::exists($report->file_path)) {
            // Generate the file if it doesn't exist
            $filePath = $this->exportReport($report, $report->format);
            $report->update(['file_path' => $filePath]);
        }

        return Storage::download($report->file_path, $report->name . '.' . $report->format);
    }

    /**
     * Delete a report and its associated file
     */
    public function deleteReport(Report $report): void
    {
        if ($report->file_path && Storage::exists($report->file_path)) {
            Storage::delete($report->file_path);
        }

        $report->delete();
    }

    /**
     * Generate Daily Sales Summary Report
     */
    public function generateDailySalesSummary(int $tenantId, ?int $branchId, string $date): array
    {
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        $ordersQuery = $this->getOrdersQuery($tenantId, $branchId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled');

        // Get basic sales metrics
        $totalSales = $ordersQuery->sum('total_amount');
        $totalInvoices = $ordersQuery->count();
        $averageInvoiceValue = $totalInvoices > 0 ? $totalSales / $totalInvoices : 0;
        $totalTax = $ordersQuery->sum('tax_amount');

        // Get electronic vs cash sales
        $electronicSales = $ordersQuery->clone()
            ->whereHas('payments', function ($q) {
                $q->whereHas('paymentMethod', function ($pm) {
                    $pm->where('code', '!=', 'cash');
                });
            })
            ->sum('total_amount');

        $cashSales = $totalSales - $electronicSales;

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'total_sales' => round($totalSales, 2),
            'electronic_sales' => round($electronicSales, 2),
            'cash_sales' => round($cashSales, 2),
            'total_invoices' => $totalInvoices,
            'average_invoice_value' => round($averageInvoiceValue, 2),
            'total_tax' => round($totalTax, 2),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Daily Stock/Drawer Report
     */
    public function generateDailyStockDrawer(int $tenantId, ?int $branchId, string $date): array
    {
        // Get branch inventory data
        $branchInventoryQuery = DB::table('branch_inventories as bi')
            ->join('branches as b', 'bi.branch_id', '=', 'b.id')
            ->join('products as p', 'bi.product_id', '=', 'p.id')
            ->where('b.tenant_id', $tenantId);

        if ($branchId) {
            $branchInventoryQuery->where('bi.branch_id', $branchId);
        }

        $inventoryData = $branchInventoryQuery
            ->select([
                'p.name as item_name',
                'p.sku',
                'bi.current_stock as available_quantity',
                'bi.minimum_level as minimum_threshold',
                'p.unit',
                'bi.branch_id'
            ])
            ->get();

        // Get sold quantities for the date
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        $soldQuantities = DB::table('order_items as oi')
            ->join('orders as o', 'oi.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('menu_items as mi', 'oi.menu_item_id', '=', 'mi.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDate, $endDate])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $soldQuantities->where('o.branch_id', $branchId);
        }

        $soldData = $soldQuantities
            ->select([
                'mi.name as item_name',
                'mi.sku',
                DB::raw('SUM(oi.quantity) as sold_quantity'),
                'o.branch_id'
            ])
            ->groupBy(['mi.name', 'mi.sku', 'o.branch_id'])
            ->get();

        // Identify low stock items
        $lowStockItems = $inventoryData->filter(function ($item) {
            return $item->available_quantity <= $item->minimum_threshold;
        });

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'inventory_summary' => [
                'total_items' => $inventoryData->count(),
                'low_stock_items' => $lowStockItems->count(),
            ],
            'inventory_details' => $inventoryData->toArray(),
            'sold_quantities' => $soldData->toArray(),
            'low_stock_alerts' => $lowStockItems->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Daily Payments Report
     */
    public function generateDailyPayments(int $tenantId, ?int $branchId, string $date): array
    {
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        $paymentsQuery = DB::table('payments as p')
            ->join('transactions as t', 'p.transaction_id', '=', 't.id')
            ->join('orders as o', 't.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('payment_methods as pm', 'p.payment_method_id', '=', 'pm.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('p.payment_date', [$startDate, $endDate])
            ->where('p.status', 'completed');

        if ($branchId) {
            $paymentsQuery->where('o.branch_id', $branchId);
        }

        $paymentsByMethod = $paymentsQuery
            ->select([
                'pm.name as payment_method',
                'pm.code as payment_code',
                DB::raw('COUNT(p.id) as transaction_count'),
                DB::raw('SUM(p.amount) as total_amount'),
                DB::raw('AVG(p.amount) as average_amount')
            ])
            ->groupBy(['pm.name', 'pm.code'])
            ->get();

        $totalPayments = $paymentsByMethod->sum('total_amount');
        $totalTransactions = $paymentsByMethod->sum('transaction_count');

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'summary' => [
                'total_amount' => round($totalPayments, 2),
                'total_transactions' => $totalTransactions,
                'average_transaction' => $totalTransactions > 0 ? round($totalPayments / $totalTransactions, 2) : 0,
            ],
            'payment_methods' => $paymentsByMethod->map(function ($item) use ($totalPayments) {
                return [
                    'payment_method' => $item->payment_method,
                    'payment_code' => $item->payment_code,
                    'transaction_count' => $item->transaction_count,
                    'total_amount' => round($item->total_amount, 2),
                    'average_amount' => round($item->average_amount, 2),
                    'percentage' => $totalPayments > 0 ? round(($item->total_amount / $totalPayments) * 100, 2) : 0,
                ];
            })->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Daily Discounts Report
     */
    public function generateDailyDiscounts(int $tenantId, ?int $branchId, string $date): array
    {
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        $ordersQuery = $this->getOrdersQuery($tenantId, $branchId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled');

        $totalSales = $ordersQuery->sum('total_amount');
        $totalDiscounts = $ordersQuery->sum('discount_amount');

        // Get discount details
        $discountDetails = DB::table('order_discounts as od')
            ->join('orders as o', 'od.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->leftJoin('discounts as d', 'od.discount_id', '=', 'd.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDate, $endDate])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $discountDetails->where('o.branch_id', $branchId);
        }

        $discountsByType = $discountDetails
            ->select([
                'od.discount_type',
                'd.name as discount_name',
                DB::raw('COUNT(od.id) as usage_count'),
                DB::raw('SUM(od.discount_amount) as total_discount'),
                DB::raw('AVG(od.discount_amount) as average_discount')
            ])
            ->groupBy(['od.discount_type', 'd.name'])
            ->get();

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'summary' => [
                'total_sales' => round($totalSales, 2),
                'total_discounts' => round($totalDiscounts, 2),
                'discount_percentage' => $totalSales > 0 ? round(($totalDiscounts / $totalSales) * 100, 2) : 0,
                'orders_with_discounts' => $ordersQuery->where('discount_amount', '>', 0)->count(),
            ],
            'discount_details' => $discountsByType->map(function ($item) use ($totalDiscounts) {
                return [
                    'discount_type' => $item->discount_type,
                    'discount_name' => $item->discount_name ?? 'Manual Discount',
                    'usage_count' => $item->usage_count,
                    'total_discount' => round($item->total_discount, 2),
                    'average_discount' => round($item->average_discount, 2),
                    'percentage_of_total' => $totalDiscounts > 0 ? round(($item->total_discount / $totalDiscounts) * 100, 2) : 0,
                ];
            })->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Daily Returns Report
     */
    public function generateDailyReturns(int $tenantId, ?int $branchId, string $date): array
    {
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        // Get returned orders (assuming status 'returned' or 'refunded')
        $returnedOrdersQuery = $this->getOrdersQuery($tenantId, $branchId)
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->whereIn('status', ['returned', 'refunded']);

        $returnedOrders = $returnedOrdersQuery->get();
        $totalReturnValue = $returnedOrders->sum('total_amount');
        $totalReturns = $returnedOrders->count();

        // Get return reasons (if available in order notes or separate table)
        $returnReasons = $returnedOrders->groupBy(function ($order) {
            // Extract return reason from notes or use default
            $notes = $order->notes ?? '';
            if (str_contains(strtolower($notes), 'quality')) return 'Quality Issues';
            if (str_contains(strtolower($notes), 'wrong')) return 'Wrong Order';
            if (str_contains(strtolower($notes), 'late')) return 'Late Delivery';
            if (str_contains(strtolower($notes), 'customer')) return 'Customer Request';
            return 'Other';
        })->map(function ($orders, $reason) {
            return [
                'reason' => $reason,
                'count' => $orders->count(),
                'total_value' => round($orders->sum('total_amount'), 2),
            ];
        })->values();

        // Get returned items details
        $returnedItems = DB::table('order_items as oi')
            ->join('orders as o', 'oi.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('menu_items as mi', 'oi.menu_item_id', '=', 'mi.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.updated_at', [$startDate, $endDate])
            ->whereIn('o.status', ['returned', 'refunded']);

        if ($branchId) {
            $returnedItems->where('o.branch_id', $branchId);
        }

        $itemsReturned = $returnedItems
            ->select([
                'mi.name as item_name',
                'mi.sku',
                DB::raw('SUM(oi.quantity) as returned_quantity'),
                DB::raw('SUM(oi.total_price) as returned_value')
            ])
            ->groupBy(['mi.name', 'mi.sku'])
            ->get();

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'summary' => [
                'total_returns' => $totalReturns,
                'total_return_value' => round($totalReturnValue, 2),
                'average_return_value' => $totalReturns > 0 ? round($totalReturnValue / $totalReturns, 2) : 0,
            ],
            'return_reasons' => $returnReasons->toArray(),
            'returned_items' => $itemsReturned->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Daily Cash Drawer Report
     */
    public function generateDailyCashDrawer(int $tenantId, ?int $branchId, string $date): array
    {
        $startDate = Carbon::parse($date)->startOfDay();
        $endDate = Carbon::parse($date)->endOfDay();

        // Get cash payments for the day
        $cashPayments = DB::table('payments as p')
            ->join('transactions as t', 'p.transaction_id', '=', 't.id')
            ->join('orders as o', 't.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('payment_methods as pm', 'p.payment_method_id', '=', 'pm.id')
            ->where('b.tenant_id', $tenantId)
            ->where('pm.code', 'cash')
            ->whereBetween('p.payment_date', [$startDate, $endDate])
            ->where('p.status', 'completed');

        if ($branchId) {
            $cashPayments->where('o.branch_id', $branchId);
        }

        $totalCashSales = $cashPayments->sum('p.amount');
        $cashTransactionCount = $cashPayments->count();

        // Assuming opening amount is stored in settings or cash drawer table
        // For now, we'll use a default or calculate from previous day
        $openingAmount = 200.00; // This should come from actual cash drawer opening

        // Calculate expected closing amount
        $expectedClosingAmount = $openingAmount + $totalCashSales;

        // In a real system, you'd get the actual closing amount from cash drawer close record
        $actualClosingAmount = $expectedClosingAmount; // Placeholder

        $variance = $actualClosingAmount - $expectedClosingAmount;

        return [
            'date' => $date,
            'branch_id' => $branchId,
            'cash_drawer_summary' => [
                'opening_amount' => round($openingAmount, 2),
                'cash_sales' => round($totalCashSales, 2),
                'expected_closing' => round($expectedClosingAmount, 2),
                'actual_closing' => round($actualClosingAmount, 2),
                'variance' => round($variance, 2),
                'transaction_count' => $cashTransactionCount,
            ],
            'cash_transactions' => $cashPayments
                ->select([
                    'o.order_number',
                    'p.amount',
                    'p.payment_date',
                    'p.change_amount'
                ])
                ->orderBy('p.payment_date')
                ->get()
                ->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Product Performance Report
     */
    public function generateProductPerformance(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        $productSalesQuery = DB::table('order_items as oi')
            ->join('orders as o', 'oi.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('menu_items as mi', 'oi.menu_item_id', '=', 'mi.id')
            ->join('menu_categories as mc', 'mi.category_id', '=', 'mc.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $productSalesQuery->where('o.branch_id', $branchId);
        }

        $productPerformance = $productSalesQuery
            ->select([
                'mi.id as menu_item_id',
                'mi.name as item_name',
                'mi.sku',
                'mc.name as category',
                'mi.base_price',
                'mi.cost_price',
                DB::raw('SUM(oi.quantity) as total_sold'),
                DB::raw('SUM(oi.total_price) as total_revenue'),
                DB::raw('AVG(oi.unit_price) as average_price'),
                DB::raw('COUNT(DISTINCT o.id) as order_count')
            ])
            ->groupBy([
                'mi.id', 'mi.name', 'mi.sku', 'mc.name',
                'mi.base_price', 'mi.cost_price'
            ])
            ->orderBy('total_sold', 'desc')
            ->get();

        // Calculate profit for each item
        $productPerformance = $productPerformance->map(function ($item) {
            $profit = ($item->average_price - $item->cost_price) * $item->total_sold;
            $profitMargin = $item->average_price > 0 ? (($item->average_price - $item->cost_price) / $item->average_price) * 100 : 0;

            return [
                'menu_item_id' => $item->menu_item_id,
                'item_name' => $item->item_name,
                'sku' => $item->sku,
                'category' => $item->category,
                'base_price' => round($item->base_price, 2),
                'cost_price' => round($item->cost_price, 2),
                'average_selling_price' => round($item->average_price, 2),
                'total_sold' => $item->total_sold,
                'total_revenue' => round($item->total_revenue, 2),
                'total_profit' => round($profit, 2),
                'profit_margin_percent' => round($profitMargin, 2),
                'order_count' => $item->order_count,
            ];
        });

        // Get best and worst performers
        $bestSellers = $productPerformance->take(10);
        $worstPerformers = $productPerformance->sortBy('total_sold')->take(10);

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_products' => $productPerformance->count(),
                'total_revenue' => round($productPerformance->sum('total_revenue'), 2),
                'total_profit' => round($productPerformance->sum('total_profit'), 2),
                'total_items_sold' => $productPerformance->sum('total_sold'),
            ],
            'best_sellers' => $bestSellers->toArray(),
            'worst_performers' => $worstPerformers->values()->toArray(),
            'all_products' => $productPerformance->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Staff Performance Report
     */
    public function generateStaffPerformance(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        // Get cashier performance
        $cashierPerformance = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('users as u', 'o.cashier_id', '=', 'u.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $cashierPerformance->where('o.branch_id', $branchId);
        }

        $cashierStats = $cashierPerformance
            ->select([
                'u.id as staff_id',
                'u.name as staff_name',
                'u.email',
                DB::raw('COUNT(o.id) as total_orders'),
                DB::raw('SUM(o.total_amount) as total_sales'),
                DB::raw('AVG(o.total_amount) as average_order_value'),
                DB::raw('COUNT(CASE WHEN o.status = "returned" THEN 1 END) as returned_orders')
            ])
            ->groupBy(['u.id', 'u.name', 'u.email'])
            ->orderBy('total_sales', 'desc')
            ->get();

        // Calculate error rates and additional metrics
        $staffPerformance = $cashierStats->map(function ($staff) {
            $errorRate = $staff->total_orders > 0 ? ($staff->returned_orders / $staff->total_orders) * 100 : 0;

            return [
                'staff_id' => $staff->staff_id,
                'staff_name' => $staff->staff_name,
                'email' => $staff->email,
                'total_orders' => $staff->total_orders,
                'total_sales' => round($staff->total_sales, 2),
                'average_order_value' => round($staff->average_order_value, 2),
                'returned_orders' => $staff->returned_orders,
                'error_rate_percent' => round($errorRate, 2),
            ];
        });

        // Get server performance (if different from cashier)
        $serverPerformance = DB::table('orders as o')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('users as u', 'o.waiter_id', '=', 'u.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled')
            ->whereNotNull('o.waiter_id');

        if ($branchId) {
            $serverPerformance->where('o.branch_id', $branchId);
        }

        $serverStats = $serverPerformance
            ->select([
                'u.id as staff_id',
                'u.name as staff_name',
                'u.email',
                DB::raw('COUNT(o.id) as total_orders_served'),
                DB::raw('SUM(o.total_amount) as total_sales_served'),
                DB::raw('AVG(o.total_amount) as average_order_value')
            ])
            ->groupBy(['u.id', 'u.name', 'u.email'])
            ->orderBy('total_sales_served', 'desc')
            ->get();

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'cashier_performance' => $staffPerformance->toArray(),
            'server_performance' => $serverStats->toArray(),
            'summary' => [
                'total_cashiers' => $staffPerformance->count(),
                'total_servers' => $serverStats->count(),
                'top_cashier' => $staffPerformance->first(),
                'top_server' => $serverStats->first(),
            ],
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Generate Profit Report
     */
    public function generateProfitReport(int $tenantId, ?int $branchId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        [$start, $end] = $this->getPeriodDates($period, $startDate, $endDate);
        $startDateTime = Carbon::parse($start)->startOfDay();
        $endDateTime = Carbon::parse($end)->endOfDay();

        // Get sales and cost data
        $profitData = DB::table('order_items as oi')
            ->join('orders as o', 'oi.order_id', '=', 'o.id')
            ->join('branches as b', 'o.branch_id', '=', 'b.id')
            ->join('menu_items as mi', 'oi.menu_item_id', '=', 'mi.id')
            ->join('menu_categories as mc', 'mi.category_id', '=', 'mc.id')
            ->where('b.tenant_id', $tenantId)
            ->whereBetween('o.created_at', [$startDateTime, $endDateTime])
            ->where('o.status', '!=', 'cancelled');

        if ($branchId) {
            $profitData->where('o.branch_id', $branchId);
        }

        $profitByCategory = $profitData
            ->select([
                'mc.name as category',
                DB::raw('SUM(oi.total_price) as total_revenue'),
                DB::raw('SUM(oi.quantity * mi.cost_price) as total_cost'),
                DB::raw('SUM(oi.quantity) as total_quantity')
            ])
            ->groupBy('mc.name')
            ->get()
            ->map(function ($item) {
                $profit = $item->total_revenue - $item->total_cost;
                $profitMargin = $item->total_revenue > 0 ? ($profit / $item->total_revenue) * 100 : 0;

                return [
                    'category' => $item->category,
                    'total_revenue' => round($item->total_revenue, 2),
                    'total_cost' => round($item->total_cost, 2),
                    'total_profit' => round($profit, 2),
                    'profit_margin_percent' => round($profitMargin, 2),
                    'total_quantity' => $item->total_quantity,
                ];
            });

        $totalRevenue = $profitByCategory->sum('total_revenue');
        $totalCost = $profitByCategory->sum('total_cost');
        $totalProfit = $totalRevenue - $totalCost;
        $overallProfitMargin = $totalRevenue > 0 ? ($totalProfit / $totalRevenue) * 100 : 0;

        return [
            'period' => $period,
            'start_date' => $start,
            'end_date' => $end,
            'branch_id' => $branchId,
            'summary' => [
                'total_revenue' => round($totalRevenue, 2),
                'total_cost' => round($totalCost, 2),
                'total_profit' => round($totalProfit, 2),
                'profit_margin_percent' => round($overallProfitMargin, 2),
            ],
            'profit_by_category' => $profitByCategory->toArray(),
            'generated_at' => now()->toISOString(),
        ];
    }

    /**
     * Export report to specified format
     */
    public function exportReport(Report $report, string $format = 'pdf'): string
    {
        $data = $report->data;
        $fileName = $this->generateFileName($report, $format);
        $filePath = "reports/{$report->tenant_id}/{$fileName}";

        switch ($format) {
            case 'pdf':
                return $this->exportToPdf($data, $filePath, $report);
            case 'excel':
                return $this->exportToExcel($data, $filePath, $report);
            case 'csv':
                return $this->exportToCsv($data, $filePath, $report);
            case 'json':
                return $this->exportToJson($data, $filePath, $report);
            default:
                throw new \InvalidArgumentException("Unsupported export format: {$format}");
        }
    }

    /**
     * Generate filename for report export
     */
    private function generateFileName(Report $report, string $format): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $branchSuffix = $report->branch_id ? "_branch_{$report->branch_id}" : '_all_branches';

        return "{$report->type}_report_{$report->report_date_from}_to_{$report->report_date_to}{$branchSuffix}_{$timestamp}.{$format}";
    }

    /**
     * Export to PDF format
     */
    private function exportToPdf(array $data, string $filePath, Report $report): string
    {
        // For now, we'll create a simple HTML-to-PDF conversion
        // In a real implementation, you'd use a library like DomPDF or wkhtmltopdf

        $html = $this->generateHtmlReport($data, $report);

        // Store the HTML content as a temporary solution
        // In production, convert this to actual PDF
        $pdfPath = str_replace('.pdf', '.html', $filePath);
        Storage::put($pdfPath, $html);

        return Storage::url($pdfPath);
    }

    /**
     * Export to Excel format
     */
    private function exportToExcel(array $data, string $filePath, Report $report): string
    {
        // For now, we'll create a CSV-like format
        // In a real implementation, you'd use PhpSpreadsheet

        $csvContent = $this->generateCsvContent($data, $report);
        $excelPath = str_replace('.excel', '.csv', $filePath);
        Storage::put($excelPath, $csvContent);

        return Storage::url($excelPath);
    }

    /**
     * Export to CSV format
     */
    private function exportToCsv(array $data, string $filePath, Report $report): string
    {
        $csvContent = $this->generateCsvContent($data, $report);
        Storage::put($filePath, $csvContent);

        return Storage::url($filePath);
    }

    /**
     * Export to JSON format
     */
    private function exportToJson(array $data, string $filePath, Report $report): string
    {
        $jsonContent = json_encode($data, JSON_PRETTY_PRINT);
        Storage::put($filePath, $jsonContent);

        return Storage::url($filePath);
    }

    /**
     * Generate HTML report content
     */
    private function generateHtmlReport(array $data, Report $report): string
    {
        $html = "<!DOCTYPE html>
<html>
<head>
    <title>{$report->name}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .number { text-align: right; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>{$report->name}</h1>
        <p>Generated on: " . now()->format('Y-m-d H:i:s') . "</p>
        <p>Period: {$report->report_date_from} to {$report->report_date_to}</p>
    </div>";

        // Add summary section if available
        if (isset($data['summary'])) {
            $html .= "<div class='summary'><h2>Summary</h2>";
            foreach ($data['summary'] as $key => $value) {
                $html .= "<p><strong>" . ucwords(str_replace('_', ' ', $key)) . ":</strong> {$value}</p>";
            }
            $html .= "</div>";
        }

        // Add data tables
        foreach ($data as $section => $sectionData) {
            if ($section === 'summary' || $section === 'generated_at') continue;

            if (is_array($sectionData) && !empty($sectionData)) {
                $html .= "<h2>" . ucwords(str_replace('_', ' ', $section)) . "</h2>";
                $html .= $this->arrayToHtmlTable($sectionData);
            }
        }

        $html .= "</body></html>";

        return $html;
    }

    /**
     * Generate CSV content from report data
     */
    private function generateCsvContent(array $data, Report $report): string
    {
        $csv = "Report: {$report->name}\n";
        $csv .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $csv .= "Period: {$report->report_date_from} to {$report->report_date_to}\n\n";

        foreach ($data as $section => $sectionData) {
            if ($section === 'generated_at') continue;

            $csv .= strtoupper(str_replace('_', ' ', $section)) . "\n";

            if ($section === 'summary' && is_array($sectionData)) {
                foreach ($sectionData as $key => $value) {
                    $csv .= ucwords(str_replace('_', ' ', $key)) . "," . $value . "\n";
                }
            } elseif (is_array($sectionData) && !empty($sectionData)) {
                $csv .= $this->arrayToCsv($sectionData);
            }

            $csv .= "\n";
        }

        return $csv;
    }

    /**
     * Convert array to HTML table
     */
    private function arrayToHtmlTable(array $data): string
    {
        if (empty($data)) return '';

        $firstItem = is_array($data) ? reset($data) : $data;
        if (!is_array($firstItem)) return '';

        $html = "<table>";

        // Header
        $html .= "<tr>";
        foreach (array_keys($firstItem) as $header) {
            $html .= "<th>" . ucwords(str_replace('_', ' ', $header)) . "</th>";
        }
        $html .= "</tr>";

        // Rows
        foreach ($data as $row) {
            $html .= "<tr>";
            foreach ($row as $cell) {
                $class = is_numeric($cell) ? 'number' : '';
                $html .= "<td class='{$class}'>{$cell}</td>";
            }
            $html .= "</tr>";
        }

        $html .= "</table>";

        return $html;
    }

    /**
     * Convert array to CSV format
     */
    private function arrayToCsv(array $data): string
    {
        if (empty($data)) return '';

        $firstItem = is_array($data) ? reset($data) : $data;
        if (!is_array($firstItem)) return '';

        $csv = '';

        // Header
        $headers = array_keys($firstItem);
        $csv .= implode(',', array_map(function($header) {
            return ucwords(str_replace('_', ' ', $header));
        }, $headers)) . "\n";

        // Rows
        foreach ($data as $row) {
            $csv .= implode(',', array_map(function($cell) {
                return is_string($cell) && strpos($cell, ',') !== false ? '"' . $cell . '"' : $cell;
            }, $row)) . "\n";
        }

        return $csv;
    }
}
