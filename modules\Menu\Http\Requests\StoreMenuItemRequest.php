<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'menu_id' => 'required|exists:menus,id',
            'category_id' => 'nullable|exists:menu_categories,id',
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string',
            'base_price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'string|max:500',
            'prep_time_minutes' => 'integer|min:0',
            'calories' => 'nullable|integer|min:0',
            'nutritional_info' => 'nullable|array',
            'allergens' => 'nullable|array',
            'dietary_info' => 'nullable|array',
            'recipe_id' => 'nullable|exists:recipes,id',
            'barcode' => 'nullable|string|max:100',
            'sku' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_spicy' => 'boolean',
            'spice_level' => 'nullable|integer|between:1,5',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_id.required' => 'معرف القائمة مطلوب',
            'menu_id.exists' => 'القائمة المحددة غير موجودة',
            'category_id.exists' => 'الفئة المحددة غير موجودة',
            'name.required' => 'اسم عنصر القائمة مطلوب',
            'name.string' => 'اسم عنصر القائمة يجب أن يكون نص',
            'name.max' => 'اسم عنصر القائمة يجب ألا يتجاوز 255 حرف',
            'code.required' => 'كود عنصر القائمة مطلوب',
            'code.string' => 'كود عنصر القائمة يجب أن يكون نص',
            'code.max' => 'كود عنصر القائمة يجب ألا يتجاوز 50 حرف',
            'base_price.required' => 'السعر الأساسي مطلوب',
            'base_price.numeric' => 'السعر الأساسي يجب أن يكون رقم',
            'base_price.min' => 'السعر الأساسي يجب أن يكون على الأقل 0',
            'cost_price.numeric' => 'سعر التكلفة يجب أن يكون رقم',
            'cost_price.min' => 'سعر التكلفة يجب أن يكون على الأقل 0',
            'prep_time_minutes.integer' => 'وقت التحضير يجب أن يكون رقم صحيح',
            'prep_time_minutes.min' => 'وقت التحضير يجب أن يكون على الأقل 0',
            'calories.integer' => 'السعرات الحرارية يجب أن تكون رقم صحيح',
            'calories.min' => 'السعرات الحرارية يجب أن تكون على الأقل 0',
            'spice_level.integer' => 'مستوى الحرارة يجب أن يكون رقم صحيح',
            'spice_level.between' => 'مستوى الحرارة يجب أن يكون بين 1 و 5',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}