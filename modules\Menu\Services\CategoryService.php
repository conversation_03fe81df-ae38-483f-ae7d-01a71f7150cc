<?php

namespace Modules\Menu\Services;

use App\Models\MenuCategory;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryService
{
    /**
     * Get categories for a specific branch with pagination and filtering.
     */
    public function getCategoriesForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuCategory::with(['menu', 'parentCategory', 'childCategories'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_id'])) {
            $query->where('menu_id', $filters['menu_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['parent_category_id'])) {
            if ($filters['parent_category_id'] === 'null') {
                $query->whereNull('parent_category_id');
            } else {
                $query->where('parent_category_id', $filters['parent_category_id']);
            }
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get category by ID with relationships.
     */
    public function getCategoryById(int $id): ?MenuCategory
    {
        return MenuCategory::with(['menu', 'parentCategory', 'childCategories', 'menuItems'])
            ->withCount(['menuItems', 'childCategories'])
            ->find($id);
    }

    /**
     * Create a new category.
     */
    public function createCategory(array $data): MenuCategory
    {
        return MenuCategory::create($data);
    }

    /**
     * Update an existing category.
     */
    public function updateCategory(int $id, array $data): ?MenuCategory
    {
        $category = MenuCategory::find($id);
        
        if (!$category) {
            return null;
        }

        $category->update($data);
        
        return $category->fresh(['menu', 'parentCategory', 'childCategories']);
    }

    /**
     * Delete a category.
     */
    public function deleteCategory(int $id): bool
    {
        $category = MenuCategory::find($id);
        
        if (!$category) {
            return false;
        }

        // Check if category has child categories or menu items
        if ($category->childCategories()->exists() || $category->menuItems()->exists()) {
            throw new \Exception('Cannot delete category that has child categories or menu items');
        }

        return $category->delete();
    }

    /**
     * Get active categories for a menu.
     */
    public function getActiveCategoriesForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get root categories for a menu (categories without parent).
     */
    public function getRootCategoriesForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->whereNull('parent_category_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get category by ID for a specific branch.
     */
    public function getCategoryByIdForBranch(int $id, int $branchId): ?MenuCategory
    {
        return MenuCategory::with(['menu', 'parentCategory', 'childCategories', 'menuItems'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Update category for a specific branch.
     */
    public function updateCategoryForBranch(int $id, array $data, int $branchId): ?MenuCategory
    {
        $category = MenuCategory::whereHas('menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })->find($id);
        
        if (!$category) {
            return null;
        }

        $category->update($data);
        
        return $category->fresh(['menu', 'parentCategory', 'childCategories']);
    }

    /**
     * Delete category for a specific branch.
     */
    public function deleteCategoryForBranch(int $id, int $branchId): bool
    {
        $category = MenuCategory::whereHas('menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })->find($id);
        
        if (!$category) {
            return false;
        }

        // Check if category has child categories or menu items
        if ($category->childCategories()->exists() || $category->menuItems()->exists()) {
            throw new \Exception('Cannot delete category that has child categories or menu items');
        }

        return $category->delete();
    }

    /**
     * Create category for web interface.
     */
    public function createCategoryForWeb(array $data): MenuCategory
    {
        return $this->createCategory($data);
    }

    /**
     * Update category for web interface.
     */
    public function updateCategoryForWeb(int $id, array $data, int $branchId): ?MenuCategory
    {
        return $this->updateCategoryForBranch($id, $data, $branchId);
    }

    /**
     * Get categories for DataTable display.
     */
    public function getCategoriesForDataTable(int $branchId, $request)
    {
        $query = MenuCategory::with(['menu', 'parentCategory'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply search filter
        if ($request->has('search') && $request->search['value']) {
            $search = $request->search['value'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply menu filter
        if ($request->has('menu_id') && $request->menu_id) {
            $query->where('menu_id', $request->menu_id);
        }

        // Apply status filter
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->is_active);
        }

        return datatables($query)
            ->addIndexColumn()
            ->addColumn('action', function ($category) {
                return $this->getActionButtons($category);
            })
            ->editColumn('is_active', function ($category) {
                return $category->is_active 
                    ? '<span class="badge badge-success">نشط</span>' 
                    : '<span class="badge badge-danger">غير نشط</span>';
            })
            ->addColumn('menu_name', function ($category) {
                return $category->menu ? $category->menu->name : '-';
            })
            ->editColumn('created_at', function ($category) {
                return $category->created_at ? $category->created_at->format('Y-m-d H:i:s') : '-';
            })
            ->rawColumns(['action', 'is_active'])
            ->make(true);
    }

    /**
     * Get action buttons for DataTable.
     */
    private function getActionButtons($category)
    {
        return '
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-info show-category" data-id="' . $category->id . '" title="عرض">
                    <i class="fa fa-eye"></i>
                </button>
                <button type="button" class="btn btn-sm btn-primary edit-category" data-id="' . $category->id . '" title="تعديل">
                    <i class="fa fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger delete-category" data-id="' . $category->id . '" title="حذف">
                    <i class="fa fa-trash"></i>
                </button>
            </div>';
    }

    /**
     * Get categories list for dropdowns.
     */
    public function getCategoriesListForBranch(int $branchId, ?int $menuId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = MenuCategory::select('id', 'name', 'menu_id', 'parent_category_id')
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->where('is_active', true)
            ->orderBy('sort_order');

        if ($menuId) {
            $query->where('menu_id', $menuId);
        }

        return $query->get();
    }
}