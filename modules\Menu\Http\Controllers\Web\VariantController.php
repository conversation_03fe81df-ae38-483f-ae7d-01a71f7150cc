<?php

namespace Modules\Menu\Http\Controllers\Web;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;use Modules\Menu\Http\Requests\StoreVariantRequest;
use Modules\Menu\Http\Requests\UpdateVariantRequest;
use Modules\Menu\Services\VariantService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class VariantController extends Controller
{
    protected $variantService;

    public function __construct(VariantService $variantService)
    {
        $this->variantService = $variantService;
    }

    /**
     * Display a listing of variants for DataTable
     */
    public function index(Request $request)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            if ($request->ajax()) {
                return $this->variantService->getVariantsForDataTable($branchId, $request);
            }

            return view('menu::variations');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve variants: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created variant
     */
    public function store(StoreVariantRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $variant = $this->variantService->createVariantForWeb($data);
            
            return ResponseHelper::success('Variant created successfully', $variant);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create variant: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified variant
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $variant = $this->variantService->getVariantByIdForBranch($id, $branchId);
            
            if (!$variant) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant retrieved successfully', $variant);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve variant: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified variant
     */
    public function edit(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $variant = $this->variantService->getVariantByIdForBranch($id, $branchId);
            
            if (!$variant) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant retrieved for editing', $variant);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve variant: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified variant
     */
    public function update(UpdateVariantRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $variant = $this->variantService->updateVariantForWeb($id, $request->validated(), $branchId);
            
            if (!$variant) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant updated successfully', $variant);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update variant: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified variant
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->variantService->deleteVariantForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Variant not found');
            }
            
            return ResponseHelper::success('Variant deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete variant: ' . $e->getMessage());
        }
    }
}