@extends('layouts.master')

@section('css')
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}
.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
    margin-bottom: 1rem;
}
.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}
.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}
.export-buttons {
    margin-bottom: 1rem;
}
.table-responsive {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">{{ $report->name }}</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ التقارير</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('reports.index') }}" class="btn btn-secondary btn-icon ml-2">
                <i class="mdi mdi-arrow-right"></i>
            </a>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" onclick="refreshReport()">
                <i class="mdi mdi-refresh"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

<!-- Report Header -->
<div class="report-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-2">{{ $report->name }}</h2>
            <p class="mb-0">
                <i class="mdi mdi-calendar mr-2"></i>
                @if($report->start_date && $report->end_date)
                    من {{ \Carbon\Carbon::parse($report->start_date)->format('Y-m-d') }} 
                    إلى {{ \Carbon\Carbon::parse($report->end_date)->format('Y-m-d') }}
                @else
                    {{ $report->period ?? 'فترة مخصصة' }}
                @endif
            </p>
            @if($report->branch_id)
                <p class="mb-0">
                    <i class="mdi mdi-store mr-2"></i>
                    الفرع: {{ $report->branch->name ?? 'غير محدد' }}
                </p>
            @endif
        </div>
        <div class="col-md-4 text-right">
            <div class="export-buttons">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-light" onclick="exportReport('pdf')">
                        <i class="mdi mdi-file-pdf mr-1"></i>PDF
                    </button>
                    <button type="button" class="btn btn-light" onclick="exportReport('excel')">
                        <i class="mdi mdi-file-excel mr-1"></i>Excel
                    </button>
                    <button type="button" class="btn btn-light" onclick="exportReport('csv')">
                        <i class="mdi mdi-file-delimited mr-1"></i>CSV
                    </button>
                </div>
            </div>
            <small class="d-block mt-2">
                آخر تحديث: {{ $report->updated_at->diffForHumans() }}
            </small>
        </div>
    </div>
</div>

@if($report->status === 'completed' && $report->data)
    @php $data = json_decode($report->data, true); @endphp
    
    <!-- Summary Statistics -->
    @if(isset($data['summary']))
        <div class="row">
            @foreach($data['summary'] as $key => $value)
                <div class="col-xl-3 col-lg-4 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-value">
                            @if(is_numeric($value))
                                {{ number_format($value, 2) }}
                            @else
                                {{ $value }}
                            @endif
                        </div>
                        <div class="stat-label">{{ translateStatKey($key) }}</div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Charts Section -->
    @if(in_array($report->type, ['daily_sales_summary', 'hourly_sales', 'product_performance']))
        <div class="row">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">الرسم البياني</h5>
                    <canvas id="mainChart" height="300"></canvas>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-container">
                    <h5 class="mb-3">التوزيع</h5>
                    <canvas id="pieChart" height="300"></canvas>
                </div>
            </div>
        </div>
    @endif

    <!-- Detailed Data Tables -->
    @if(isset($data['hourly_breakdown']) && $report->type === 'hourly_sales')
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">تفصيل المبيعات بالساعة</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="hourlyTable">
                        <thead>
                            <tr>
                                <th>الساعة</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي المبيعات</th>
                                <th>متوسط قيمة الطلب</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($data['hourly_breakdown'] as $hour)
                                <tr>
                                    <td>{{ $hour['hour_display'] }}</td>
                                    <td>{{ $hour['order_count'] }}</td>
                                    <td>{{ number_format($hour['total_sales'], 2) }} ر.س</td>
                                    <td>{{ number_format($hour['average_order_value'], 2) }} ر.س</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    @if(isset($data['tax_breakdown']) && $report->type === 'tax_report')
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">تفصيل الضرائب</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="taxTable">
                        <thead>
                            <tr>
                                <th>معدل الضريبة (%)</th>
                                <th>عدد الطلبات</th>
                                <th>المبلغ قبل الضريبة</th>
                                <th>مبلغ الضريبة</th>
                                <th>المبلغ الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($data['tax_breakdown'] as $tax)
                                <tr>
                                    <td>{{ $tax['tax_rate'] }}%</td>
                                    <td>{{ $tax['order_count'] }}</td>
                                    <td>{{ number_format($tax['subtotal'], 2) }} ر.س</td>
                                    <td>{{ number_format($tax['tax_amount'], 2) }} ر.س</td>
                                    <td>{{ number_format($tax['total_amount'], 2) }} ر.س</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    @if(isset($data['void_cancelled_orders']) && $report->type === 'void_cancelled')
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">الطلبات الملغية والمرفوضة</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="voidTable">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>الحالة</th>
                                <th>المبلغ</th>
                                <th>السبب</th>
                                <th>تاريخ الإلغاء</th>
                                <th>ألغي بواسطة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($data['void_cancelled_orders'] as $order)
                                <tr>
                                    <td>{{ $order['order_number'] }}</td>
                                    <td>
                                        <span class="badge badge-{{ $order['status'] === 'cancelled' ? 'warning' : 'danger' }}">
                                            {{ $order['status'] === 'cancelled' ? 'ملغي' : 'مرفوض' }}
                                        </span>
                                    </td>
                                    <td>{{ number_format($order['total_amount'], 2) }} ر.س</td>
                                    <td>{{ $order['reason'] }}</td>
                                    <td>{{ \Carbon\Carbon::parse($order['cancelled_at'])->format('Y-m-d H:i') }}</td>
                                    <td>{{ $order['cancelled_by'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    @if(isset($data['table_performance']) && $report->type === 'table_turnover')
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">أداء الطاولات</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tableTable">
                        <thead>
                            <tr>
                                <th>رقم الطاولة</th>
                                <th>السعة</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي الإيرادات</th>
                                <th>متوسط وقت الجلوس (دقيقة)</th>
                                <th>متوسط الدورات/اليوم</th>
                                <th>الإيراد/مقعد</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($data['table_performance'] as $table)
                                <tr>
                                    <td>{{ $table['table_number'] }}</td>
                                    <td>{{ $table['capacity'] }}</td>
                                    <td>{{ $table['total_orders'] }}</td>
                                    <td>{{ number_format($table['total_revenue'], 2) }} ر.س</td>
                                    <td>{{ $table['average_dining_time_minutes'] }}</td>
                                    <td>{{ $table['average_turnovers_per_day'] }}</td>
                                    <td>{{ number_format($table['revenue_per_seat'], 2) }} ر.س</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

@elseif($report->status === 'processing')
    <div class="alert alert-info text-center">
        <div class="spinner-border text-info mr-3" role="status">
            <span class="sr-only">جاري التحميل...</span>
        </div>
        جاري معالجة التقرير... يرجى الانتظار
    </div>
@elseif($report->status === 'failed')
    <div class="alert alert-danger text-center">
        <i class="mdi mdi-alert-circle mr-2"></i>
        فشل في إنشاء التقرير. يرجى المحاولة مرة أخرى.
    </div>
@endif

@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTables
    $('.table').DataTable({
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        }
    });
    
    @if($report->status === 'completed' && $report->data)
        @php $data = json_decode($report->data, true); @endphp
        
        // Initialize charts based on report type
        @if($report->type === 'hourly_sales' && isset($data['hourly_breakdown']))
            initializeHourlySalesChart(@json($data['hourly_breakdown']));
        @endif
        
        @if($report->type === 'tax_report' && isset($data['tax_breakdown']))
            initializeTaxChart(@json($data['tax_breakdown']));
        @endif
    @endif
});

function initializeHourlySalesChart(data) {
    const ctx = document.getElementById('mainChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.hour_display),
                datasets: [{
                    label: 'المبيعات',
                    data: data.map(item => item.total_sales),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'المبيعات بالساعة'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    const pieCtx = document.getElementById('pieChart');
    if (pieCtx) {
        const topHours = data.sort((a, b) => b.total_sales - a.total_sales).slice(0, 6);
        new Chart(pieCtx, {
            type: 'doughnut',
            data: {
                labels: topHours.map(item => item.hour_display),
                datasets: [{
                    data: topHours.map(item => item.total_sales),
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'أفضل 6 ساعات مبيعاً'
                    }
                }
            }
        });
    }
}

function initializeTaxChart(data) {
    const ctx = document.getElementById('mainChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => `${item.tax_rate}%`),
                datasets: [{
                    label: 'مبلغ الضريبة',
                    data: data.map(item => item.tax_amount),
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'الضرائب حسب المعدل'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

function exportReport(format) {
    window.location.href = `/reports/{{ $report->id }}/download?format=${format}`;
}

function refreshReport() {
    window.location.reload();
}
</script>

@php
function translateStatKey($key) {
    $translations = [
        'total_sales' => 'إجمالي المبيعات',
        'total_orders' => 'إجمالي الطلبات',
        'average_order_value' => 'متوسط قيمة الطلب',
        'total_tax_collected' => 'إجمالي الضرائب',
        'total_void_cancelled' => 'إجمالي الطلبات الملغية',
        'total_amount_lost' => 'إجمالي المبلغ المفقود',
        'cancelled_orders' => 'الطلبات الملغية',
        'void_orders' => 'الطلبات المرفوضة',
        'total_tables_used' => 'إجمالي الطاولات المستخدمة',
        'total_table_orders' => 'إجمالي طلبات الطاولات',
        'total_table_revenue' => 'إجمالي إيرادات الطاولات',
        'average_dining_time' => 'متوسط وقت الجلوس',
        'average_daily_turnovers' => 'متوسط الدورات اليومية',
        'peak_hour' => 'ساعة الذروة',
        'peak_hour_sales' => 'مبيعات ساعة الذروة',
        'average_hourly_sales' => 'متوسط المبيعات بالساعة'
    ];
    
    return $translations[$key] ?? $key;
}
@endphp

@endsection
