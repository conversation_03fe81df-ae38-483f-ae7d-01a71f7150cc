@extends('layouts.master')

@section('css')
<!-- DataTables CSS -->
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
<style>
.menu-item-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: transform 0.2s ease;
}
.menu-item-thumbnail:hover {
    transform: scale(1.1);
    border-color: #007bff;
}
.menu-item-thumbnail-large {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 12px;
    border: 3px solid #e9ecef;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.default-image-placeholder, .menu-item-thumbnail-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 20px;
    border: 2px solid #e9ecef;
}
.default-image-placeholder-large {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 48px;
    border: 3px solid #e9ecef;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة عناصر القائمة</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ عناصر القائمة</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-item-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة عناصر القائمة</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع عناصر القوائم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="menu-items-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">الصورة</th>
                                <th class="border-bottom-0">اسم العنصر</th>
                                <th class="border-bottom-0">الفئة</th>
                                <th class="border-bottom-0">السعر</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Menu Item Modal -->
<div class="modal fade" id="menuItemModal" tabindex="-1" role="dialog" aria-labelledby="menuItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="menuItemModalLabel">إضافة عنصر جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="menuItemForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="item_id" name="id">
                    <input type="hidden" id="menu_id" name="menu_id" value="1">
                    
                    <!-- Basic Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">الاسم <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code">الكود <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="code" name="code" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="category_id">الفئة</label>
                                        <select class="form-control select2" id="category_id" name="category_id">
                                            <option value="">اختر الفئة</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sku">رمز المنتج (SKU)</label>
                                        <input type="text" class="form-control" id="sku" name="sku">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="description">الوصف</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="short_description">الوصف المختصر</label>
                                        <textarea class="form-control" id="short_description" name="short_description" rows="2"></textarea>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات التسعير</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="base_price">السعر الأساسي <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" class="form-control" id="base_price" name="base_price" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cost_price">سعر التكلفة</label>
                                        <input type="number" step="0.01" class="form-control" id="cost_price" name="cost_price">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preparation & Nutrition -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">التحضير والتغذية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="prep_time_minutes">وقت التحضير (بالدقائق)</label>
                                        <input type="number" class="form-control" id="prep_time_minutes" name="prep_time_minutes">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="calories">السعرات الحرارية</label>
                                        <input type="number" class="form-control" id="calories" name="calories">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_spicy">حار</label>
                                        <select class="form-control" id="is_spicy" name="is_spicy">
                                            <option value="0">لا</option>
                                            <option value="1">نعم</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="spice_level">مستوى الحرارة (1-5)</label>
                                        <select class="form-control" id="spice_level" name="spice_level">
                                            <option value="">اختر المستوى</option>
                                            <option value="1">1 - خفيف</option>
                                            <option value="2">2 - متوسط خفيف</option>
                                            <option value="3">3 - متوسط</option>
                                            <option value="4">4 - حار</option>
                                            <option value="5">5 - حار جداً</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="barcode">الباركود</label>
                                        <input type="text" class="form-control" id="barcode" name="barcode">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="recipe_id">الوصفة</label>
                                        <select class="form-control select2" id="recipe_id" name="recipe_id">
                                            <option value="">اختر الوصفة</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings & Status -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">الإعدادات والحالة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="is_active">الحالة</label>
                                        <select class="form-control" id="is_active" name="is_active">
                                            <option value="1">نشط</option>
                                            <option value="0">غير نشط</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="is_featured">مميز</label>
                                        <select class="form-control" id="is_featured" name="is_featured">
                                            <option value="0">لا</option>
                                            <option value="1">نعم</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="sort_order">ترتيب العرض</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="image">الصورة</label>
                                        <input type="file" class="form-control-file" id="image" name="image" accept="image/*">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-item-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Menu Item Modal -->
<div class="modal fade" id="showMenuItemModal" tabindex="-1" role="dialog" aria-labelledby="showMenuItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showMenuItemModalLabel">تفاصيل العنصر</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Basic Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">المعلومات الأساسية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الاسم:</strong></label>
                                    <p id="show_name"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الكود:</strong></label>
                                    <p id="show_code"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الفئة:</strong></label>
                                    <p id="show_category"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>رمز المنتج (SKU):</strong></label>
                                    <p id="show_sku"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><strong>الوصف:</strong></label>
                                    <p id="show_description"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><strong>الوصف المختصر:</strong></label>
                                    <p id="show_short_description"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">معلومات التسعير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>السعر الأساسي:</strong></label>
                                    <p id="show_base_price"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>سعر التكلفة:</strong></label>
                                    <p id="show_cost_price"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preparation & Nutrition -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">التحضير والتغذية</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>وقت التحضير:</strong></label>
                                    <p id="show_prep_time_minutes"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>السعرات الحرارية:</strong></label>
                                    <p id="show_calories"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>حار:</strong></label>
                                    <p id="show_is_spicy"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>مستوى الحرارة:</strong></label>
                                    <p id="show_spice_level"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الباركود:</strong></label>
                                    <p id="show_barcode"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>الوصفة:</strong></label>
                                    <p id="show_recipe"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings & Status -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">الإعدادات والحالة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><strong>الحالة:</strong></label>
                                    <p id="show_status"></p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><strong>مميز:</strong></label>
                                    <p id="show_is_featured"></p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><strong>ترتيب العرض:</strong></label>
                                    <p id="show_sort_order"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><strong>الصورة:</strong></label>
                                    <div id="show_image"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ الإنشاء:</strong></label>
                                    <p id="show_created_at"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>تاريخ التحديث:</strong></label>
                                    <p id="show_updated_at"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<!-- DataTables JS -->
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<!-- Select2 JS -->
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<!-- Sweet Alert JS -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#menuItemModal')
    });

    // Load categories for select dropdown
    function loadCategories() {
        $.ajax({
            url: '{{ route("categories.index") }}',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر الفئة</option>';
                $.each(response.data, function(index, category) {
                    options += '<option value="' + category.id + '">' + category.name + '</option>';
                });
                $('#category_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading categories:', xhr);
            }
        });
    }

    // Load categories on page load
    loadCategories();

    // Initialize DataTable with server-side processing
    var table = $('#menu-items-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("menu-items.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'image', name: 'image', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'category_name', name: 'category.name' },
            { data: 'price', name: 'price' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Add Menu Item Button
    $('#add-item-btn').click(function() {
        $('#menuItemForm')[0].reset();
        $('#item_id').val('');
        $('#menuItemModalLabel').text('إضافة عنصر جديد');
        $('.form-control').removeClass('is-invalid');
        $('.select2').trigger('change');
        $('#menuItemModal').modal('show');
    });

    // Edit Menu Item Button
    $(document).on('click', '.edit-item', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("menu-items.edit", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                $('#item_id').val(response.id);
                $('#name').val(response.name);
                $('#code').val(response.code);
                $('#category_id').val(response.category_id).trigger('change');
                $('#sku').val(response.sku);
                $('#description').val(response.description);
                $('#short_description').val(response.short_description);
                $('#base_price').val(response.base_price);
                $('#cost_price').val(response.cost_price);
                $('#prep_time_minutes').val(response.prep_time_minutes);
                $('#calories').val(response.calories);
                $('#is_spicy').val(response.is_spicy);
                $('#spice_level').val(response.spice_level);
                $('#barcode').val(response.barcode);
                $('#recipe_id').val(response.recipe_id).trigger('change');
                $('#is_active').val(response.is_active);
                $('#is_featured').val(response.is_featured);
                $('#sort_order').val(response.sort_order);
                $('#menuItemModalLabel').text('تعديل العنصر');
                $('.form-control').removeClass('is-invalid');
                $('#menuItemModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات العنصر', 'error');
            }
        });
    });

    // Show Menu Item Button
    $(document).on('click', '.show-item', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("menu-items.show", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var item = response;
                
                // Basic Information
                $('#show_name').text(item.name || '-');
                $('#show_code').text(item.code || '-');
                $('#show_category').text(item.category ? item.category.name : '-');
                $('#show_sku').text(item.sku || '-');
                $('#show_description').text(item.description || '-');
                $('#show_short_description').text(item.short_description || '-');
                
                // Pricing Information
                $('#show_base_price').text(item.base_price ? item.base_price + ' ريال' : '-');
                $('#show_cost_price').text(item.cost_price ? item.cost_price + ' ريال' : '-');
                
                // Preparation & Nutrition
                $('#show_prep_time_minutes').text(item.prep_time_minutes ? item.prep_time_minutes + ' دقيقة' : '-');
                $('#show_calories').text(item.calories ? item.calories + ' سعرة حرارية' : '-');
                $('#show_is_spicy').html(item.is_spicy == 1 ? '<span class="badge badge-warning">حار</span>' : '<span class="badge badge-secondary">غير حار</span>');
                $('#show_spice_level').text(item.spice_level || '-');
                $('#show_barcode').text(item.barcode || '-');
                $('#show_recipe').text(item.recipe_id || '-');
                
                // Settings & Status
                $('#show_status').html(item.is_active == 1 ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                $('#show_is_featured').html(item.is_featured == 1 ? '<span class="badge badge-primary">مميز</span>' : '<span class="badge badge-secondary">غير مميز</span>');
                $('#show_sort_order').text(item.sort_order || '-');
                
                // Show image if exists
                if (item.image && item.image !== '') {
                    $('#show_image').html('<img src="' + item.image + '" alt="صورة العنصر" class="menu-item-thumbnail-large">');
                } else {
                    $('#show_image').html('<div class="default-image-placeholder-large"><i class="mdi mdi-food"></i></div>');
                }
                
                $('#show_created_at').text(new Date(item.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(item.updated_at).toLocaleDateString('ar-SA'));
                $('#showMenuItemModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات العنصر', 'error');
            }
        });
    });

    // Delete Menu Item Button
    $(document).on('click', '.delete-item', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '{{ route("menu-items.destroy", ":id") }}'.replace(':id', id),
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف العنصر بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف العنصر", "error");
                }
            });
        });
    });

    // Save Menu Item Form
    $('#menuItemForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#item_id').val();
        var url = id ? '{{ route("menu-items.update", ":id") }}'.replace(':id', id) : '{{ route("menu-items.store") }}';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#menuItemModal').modal('hide');
                swal("نجح!", id ? "تم تحديث العنصر بنجاح" : "تم إضافة العنصر بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ العنصر", "error");
                }
            }
        });
    });
});
</script>
@endsection