<?php $__env->startSection('css'); ?>
<!-- DataTables CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.css')); ?>" rel="stylesheet">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة القوائم</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ القوائم</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-menu-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة القوائم</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع قوائم المطعم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="menus-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم القائمة</th>

          
                                <th class="border-bottom-0">الحالة</th>
                         
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Menu Modal -->
<div class="modal fade" id="menuModal" tabindex="-1" role="dialog" aria-labelledby="menuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="menuModalLabel">إضافة قائمة جديدة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="menuForm">
                <div class="modal-body">
                    <input type="hidden" id="menu_id" name="menu_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم القائمة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name_en">اسم القائمة (إنجليزي)</label>
                                <input type="text" class="form-control" id="name_en" name="name_en">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="is_active">الحالة</label>
                                <select class="form-control" id="is_active" name="is_active">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sort_order">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-menu-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Menu Modal -->
<div class="modal fade" id="showMenuModal" tabindex="-1" role="dialog" aria-labelledby="showMenuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showMenuModalLabel">تفاصيل القائمة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>اسم القائمة:</strong></label>
                            <p id="show_name"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>اسم القائمة (إنجليزي):</strong></label>
                            <p id="show_name_en"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_status"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>ترتيب العرض:</strong></label>
                            <p id="show_sort_order"></p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ الإنشاء:</strong></label>
                            <p id="show_created_at"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ التحديث:</strong></label>
                            <p id="show_updated_at"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- DataTables JS -->
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jszip.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/pdfmake.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/vfs_fonts.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.print.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')); ?>"></script>
<!-- Select2 JS -->
<script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
<!-- Sweet Alert JS -->
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize DataTable with server-side processing
    var table = $('#menus-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("menus.index")); ?>',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'is_active', name: 'is_active', orderable: false },
            // { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        // responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Add Menu Button
    $('#add-menu-btn').click(function() {
        $('#menuForm')[0].reset();
        $('#menu_id').val('');
        $('#menuModalLabel').text('إضافة قائمة جديدة');
        $('.form-control').removeClass('is-invalid');
        $('#menuModal').modal('show');
    });

    // Edit Menu Button
    $(document).on('click', '.edit-menu', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(route("menus.edit", ":id")); ?>'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                $('#menu_id').val(response.id);
                $('#name').val(response.name);
                $('#name_en').val(response.name_en);
                $('#is_active').val(response.is_active);
                $('#sort_order').val(response.sort_order);
                $('#menuModalLabel').text('تعديل القائمة');
                $('.form-control').removeClass('is-invalid');
                $('#menuModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات القائمة', 'error');
            }
        });
    });

    // Show Menu Button
    $(document).on('click', '.show-menu', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(route("menus.show", ":id")); ?>'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                $('#show_name').text(response.name || '-');
                $('#show_name_en').text(response.name_en || '-');
                $('#show_status').html(response.is_active == 1 ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                $('#show_sort_order').text(response.sort_order || '-');
                $('#show_created_at').text(new Date(response.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(response.updated_at).toLocaleDateString('ar-SA'));
                $('#showMenuModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات القائمة', 'error');
            }
        });
    });

    // Delete Menu Button
    $(document).on('click', '.delete-menu', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?php echo e(route("menus.destroy", ":id")); ?>'.replace(':id', id),
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف القائمة بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف القائمة", "error");
                }
            });
        });
    });

    // Save Menu Form
    $('#menuForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#menu_id').val();
        var url = id ? '<?php echo e(route("menus.update", ":id")); ?>'.replace(':id', id) : '<?php echo e(route("menus.store")); ?>';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#menuModal').modal('hide');
                swal("نجح!", id ? "تم تحديث القائمة بنجاح" : "تم إضافة القائمة بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ القائمة", "error");
                }
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Menu\Providers/../resources/views/menus.blade.php ENDPATH**/ ?>