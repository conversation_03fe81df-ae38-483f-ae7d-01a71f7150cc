<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\BannerService;
use Modules\Menu\Http\Requests\StoreBannerRequest;
use Modules\Menu\Http\Requests\UpdateBannerRequest;
use App\Models\Banner;

/**
 * Banner Controller
 * 
 * Handles HTTP requests for banner management in the restaurant system.
 * Provides endpoints for CRUD operations, banner display, analytics,
 * and performance tracking.
 * 
 * @package Modules\Menu\Http\Controllers
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class BannerController extends Controller
{
    protected BannerService $bannerService;

    public function __construct(BannerService $bannerService)
    {
        $this->bannerService = $bannerService;
    }

    /**
     * Display a listing of banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $filters = $request->only([
                    'branch_id', 'banner_type', 'position', 'display_location', 
                    'is_active', 'is_featured', 'priority', 'search', 'sort_by', 'sort_order'
                ]);

                $perPage = $request->get('per_page', 15);
                $banners = $this->bannerService->getAllBanners($filters, $perPage);

                return response()->json([
                    'success' => true,
                    'message' => 'Banners retrieved successfully',
                    'data' => $banners
                ]);
            }

            return view('menu::banners');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve banners',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created banner
     *
     * @param StoreBannerRequest $request
     * @return JsonResponse
     */
    public function store(StoreBannerRequest $request): JsonResponse
    {
        try {
            $banner = $this->bannerService->createBanner($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Banner created successfully',
                'data' => $banner
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create banner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $banner = $this->bannerService->getBannerById($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner retrieved successfully',
                'data' => $banner
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve banner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified banner
     *
     * @param UpdateBannerRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateBannerRequest $request, int $id): JsonResponse
    {
        try {
            $banner = $this->bannerService->updateBanner($id, $request->validated());

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner updated successfully',
                'data' => $banner
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update banner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->bannerService->deleteBanner($id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete banner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get banners for display
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function display(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'display_location' => 'required|string',
                'position' => 'nullable|string',
                'branch_id' => 'nullable|integer|exists:branches,id'
            ]);

            $banners = $this->bannerService->getBannersForDisplay(
                $request->get('display_location'),
                $request->get('position'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'message' => 'Display banners retrieved successfully',
                'data' => $banners
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve display banners',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get featured banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $banners = $this->bannerService->getFeaturedBanners($branchId, $limit);

            return response()->json([
                'success' => true,
                'message' => 'Featured banners retrieved successfully',
                'data' => $banners
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve featured banners',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Record banner impression
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function impression(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'user_id' => 'nullable|integer',
                'session_id' => 'nullable|string',
                'ip_address' => 'nullable|ip',
                'user_agent' => 'nullable|string'
            ]);

            $recorded = $this->bannerService->recordImpression(
                $id,
                $request->get('user_id'),
                $request->get('session_id'),
                $request->get('ip_address'),
                $request->get('user_agent')
            );

            if (!$recorded) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found or impression limit reached'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner impression recorded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to record banner impression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Record banner click
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function click(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'user_id' => 'nullable|integer',
                'session_id' => 'nullable|string',
                'ip_address' => 'nullable|ip',
                'user_agent' => 'nullable|string',
                'click_position' => 'nullable|array'
            ]);

            $recorded = $this->bannerService->recordClick(
                $id,
                $request->get('user_id'),
                $request->get('session_id'),
                $request->get('ip_address'),
                $request->get('user_agent'),
                $request->get('click_position', [])
            );

            if (!$recorded) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found or click limit reached'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner click recorded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to record banner click',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get banner analytics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function analytics(int $id): JsonResponse
    {
        try {
            $analytics = $this->bannerService->getBannerAnalytics($id);

            if (!$analytics) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner analytics retrieved successfully',
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve banner analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get banner performance report
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function performance(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'banner_type' => 'nullable|string',
                'position' => 'nullable|string'
            ]);

            $filters = $request->only(['branch_id', 'banner_type', 'position']);
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');

            $report = $this->bannerService->getBannerPerformanceReport(array_merge($filters, [
                'date_from' => $startDate,
                'date_to' => $endDate
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Banner performance report retrieved successfully',
                'data' => $report
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve banner performance report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle banner status
     *
     * @param int $id
     * @return JsonResponse
     */
    public function toggleStatus(int $id): JsonResponse
    {
        try {
            $banner = $this->bannerService->toggleBannerStatus($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner status updated successfully',
                'data' => $banner
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update banner status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate a banner
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function duplicate(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'start_date' => 'required|date|after_or_equal:today',
                'end_date' => 'required|date|after_or_equal:start_date'
            ]);

            $duplicatedBanner = $this->bannerService->duplicateBanner($id, [
                'title' => $request->get('title'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date')
            ]);

            if (!$duplicatedBanner) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner duplicated successfully',
                'data' => $duplicatedBanner
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate banner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset banner metrics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function resetMetrics(int $id): JsonResponse
    {
        try {
            $banner = $this->bannerService->resetBannerMetrics($id);

            if (!$banner) {
                return response()->json([
                    'success' => false,
                    'message' => 'Banner not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Banner metrics reset successfully',
                'data' => $banner
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset banner metrics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get banners by type
     *
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     */
    public function byType(Request $request, string $type): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $banners = Banner::forBranch($branchId)
                ->active()
                ->byType($type)
                ->current()
                ->orderBy('priority', 'desc')
                ->orderBy('sort_order', 'asc')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'message' => "Banners of type '{$type}' retrieved successfully",
                'data' => $banners
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve banners by type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get banners by position
     *
     * @param Request $request
     * @param string $position
     * @return JsonResponse
     */
    public function byPosition(Request $request, string $position): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $displayLocation = $request->get('display_location');
            
            $banners = Banner::forBranch($branchId)
                ->active()
                ->byPosition($position)
                ->when($displayLocation, function ($query) use ($displayLocation) {
                    return $query->byDisplayLocation($displayLocation);
                })
                ->current()
                ->orderBy('priority', 'desc')
                ->orderBy('sort_order', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => "Banners at position '{$position}' retrieved successfully",
                'data' => $banners
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve banners by position',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}