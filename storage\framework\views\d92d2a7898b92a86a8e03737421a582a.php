<?php $__env->startSection('css'); ?>
<!-- DataTables CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
<!-- Sweet Alert CSS -->
<link href="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.css')); ?>" rel="stylesheet">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة المتغيرات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ المتغيرات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-variation-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة المتغيرات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع المتغيرات المتاحة</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="variations-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">اسم المتغير</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">السعر الإضافي</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">تاريخ الإنشاء</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Variation Modal -->
<div class="modal fade" id="variationModal" tabindex="-1" role="dialog" aria-labelledby="variationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variationModalLabel">إضافة متغير جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="variationForm">
                <div class="modal-body">
                    <input type="hidden" id="variation_id" name="variation_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="menu_item_id">عنصر القائمة <span class="text-danger">*</span></label>
                                <select class="form-control" id="menu_item_id" name="menu_item_id" required>
                                    <option value="">اختر عنصر القائمة</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="price">السعر <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control" id="price" name="price" required min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">اسم المتغير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name_en">اسم المتغير (إنجليزي)</label>
                                <input type="text" class="form-control" id="name_en" name="name_en">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type">نوع المتغير <span class="text-danger">*</span></label>
                                <select class="form-control" id="type" name="type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="size">الحجم</option>
                                    <option value="color">اللون</option>
                                    <option value="flavor">النكهة</option>
                                    <option value="temperature">درجة الحرارة</option>
                                    <option value="spice_level">مستوى التوابل</option>
                                    <option value="other">أخرى</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="is_active">الحالة</label>
                                <select class="form-control" id="is_active" name="is_active">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sort_order">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="color_code">كود اللون (إذا كان النوع لون)</label>
                                <input type="color" class="form-control" id="color_code" name="color_code">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image">صورة المتغير</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>ترتيب العرض:</strong></label>
                            <p id="show_sort_order"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-variation-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Variation Modal -->
<div class="modal fade" id="showVariationModal" tabindex="-1" role="dialog" aria-labelledby="showVariationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showVariationModalLabel">تفاصيل المتغير</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>عنصر القائمة:</strong></label>
                            <p id="show_menu_item"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>السعر:</strong></label>
                            <p id="show_additional_price"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>اسم المتغير:</strong></label>
                            <p id="show_name"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>اسم المتغير (إنجليزي):</strong></label>
                            <p id="show_name_en"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>النوع:</strong></label>
                            <p id="show_type"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_status"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>كود اللون:</strong></label>
                            <div id="show_color_code"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الصورة:</strong></label>
                            <div id="show_image"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ الإنشاء:</strong></label>
                            <p id="show_created_at"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ التحديث:</strong></label>
                            <p id="show_updated_at"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- DataTables JS -->
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jszip.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/pdfmake.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/vfs_fonts.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.print.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')); ?>"></script>
<!-- Select2 JS -->
<script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
<!-- Sweet Alert JS -->
<script src="<?php echo e(URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')); ?>"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize DataTable with server-side processing
    var table = $('#variations-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("variations.index")); ?>',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'type', name: 'type' },
            { data: 'price', name: 'price' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Load menu items for dropdown
    function loadMenuItems() {
        $.ajax({
            url: '<?php echo e(route("menu-items.list")); ?>',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر عنصر القائمة</option>';
                $.each(response, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.name + ' (' + item.price + ' ريال)</option>';
                });
                $('#menu_item_id').html(options);
            },
            error: function(xhr) {
                console.log('Error loading menu items:', xhr);
            }
        });
    }

    // Add Variation Button
    $('#add-variation-btn').click(function() {
        $('#variationForm')[0].reset();
        $('#variation_id').val('');
        $('#variationModalLabel').text('إضافة متغير جديد');
        $('.form-control').removeClass('is-invalid');
        loadMenuItems(); // Load menu items when opening modal
        $('#variationModal').modal('show');
    });

    // Edit Variation Button
    $(document).on('click', '.edit-variation', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(route("variations.edit", ":id")); ?>'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                loadMenuItems(); // Load menu items first
                setTimeout(function() {
                    $('#variation_id').val(response.id);
                    $('#menu_item_id').val(response.menu_item_id);
                    $('#name').val(response.name);
                    $('#name_en').val(response.name_en);
                    $('#type').val(response.type);
                    $('#price').val(response.price);
                    $('#is_active').val(response.is_active);
                    $('#sort_order').val(response.sort_order);
                    $('#color_code').val(response.color_code);
                    $('#variationModalLabel').text('تعديل المتغير');
                    $('.form-control').removeClass('is-invalid');
                    $('#variationModal').modal('show');
                }, 500); // Wait for menu items to load
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات المتغير', 'error');
            }
        });
    });

    // Show Variation Button
    $(document).on('click', '.show-variation', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '<?php echo e(route("variations.show", ":id")); ?>'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var variation = response;
                
                // Populate menu item name
                $('#show_menu_item').text(variation.menu_item ? variation.menu_item.name : 'غير محدد');
                $('#show_additional_price').text(variation.price ? variation.price + ' ريال' : '0 ريال');
                $('#show_name').text(variation.name || '-');
                $('#show_name_en').text(variation.name_en || '-');
                
                // Show type in Arabic
                var typeMap = {
                    'size': 'الحجم',
                    'color': 'اللون',
                    'flavor': 'النكهة',
                    'temperature': 'درجة الحرارة',
                    'spice_level': 'مستوى التوابل',
                    'other': 'أخرى'
                };
                $('#show_type').text(typeMap[variation.type] || variation.type || '-');
                
                $('#show_status').html(variation.is_active == 1 ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                $('#show_sort_order').text(variation.sort_order || '-');
                
                // Show color code if exists
                if (variation.color_code) {
                    $('#show_color_code').html('<div style="width: 30px; height: 30px; background-color: ' + variation.color_code + '; border: 1px solid #ccc; display: inline-block; margin-right: 10px;"></div>' + variation.color_code);
                } else {
                    $('#show_color_code').html('<span class="text-muted">لا يوجد</span>');
                }
                
                // Show image if exists
                if (variation.image) {
                    $('#show_image').html('<img src="' + variation.image + '" alt="صورة المتغير" style="max-width: 100px; max-height: 100px;">');
                } else {
                    $('#show_image').html('<span class="text-muted">لا توجد صورة</span>');
                }
                
                $('#show_created_at').text(new Date(variation.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(variation.updated_at).toLocaleDateString('ar-SA'));
                $('#showVariationModal').modal('show');
            },
            error: function(xhr) {
                swal('خطأ!', 'حدث خطأ أثناء جلب بيانات المتغير', 'error');
            }
        });
    });

    // Delete Variation Button
    $(document).on('click', '.delete-variation', function() {
        var id = $(this).data('id');
        
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false
        }, function() {
            $.ajax({
                url: '<?php echo e(route("variations.destroy", ":id")); ?>'.replace(':id', id),
                type: 'DELETE',
                success: function(response) {
                    swal("تم الحذف!", "تم حذف المتغير بنجاح.", "success");
                    table.ajax.reload();
                },
                error: function(xhr) {
                    swal("خطأ!", "حدث خطأ أثناء حذف المتغير", "error");
                }
            });
        });
    });

    // Save Variation Form
    $('#variationForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#variation_id').val();
        var url = id ? '<?php echo e(route("variations.update", ":id")); ?>'.replace(':id', id) : '<?php echo e(route("variations.store")); ?>';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#variationModal').modal('hide');
                swal("نجح!", id ? "تم تحديث المتغير بنجاح" : "تم إضافة المتغير بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $('.form-control').removeClass('is-invalid');
                    $('.invalid-feedback').text('');
                    
                    $.each(errors, function(field, messages) {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    swal("خطأ!", "حدث خطأ أثناء حفظ المتغير", "error");
                }
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Menu\Providers/../resources/views/variations.blade.php ENDPATH**/ ?>