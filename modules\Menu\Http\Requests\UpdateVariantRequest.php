<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.string' => 'اسم التنويع يجب أن يكون نص',
            'name.max' => 'اسم التنويع يجب ألا يتجاوز 100 حرف',
            'code.string' => 'كود التنويع يجب أن يكون نص',
            'code.max' => 'كود التنويع يجب ألا يتجاوز 50 حرف',
            'price_modifier.numeric' => 'معدل السعر يجب أن يكون رقم',
            'cost_modifier.numeric' => 'معدل التكلفة يجب أن يكون رقم',
            'is_default.boolean' => 'حالة الافتراضي يجب أن تكون صحيح أو خطأ',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:100',
            'code' => 'sometimes|string|max:50',
            'price_modifier' => 'sometimes|numeric',
            'cost_modifier' => 'nullable|numeric',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];
    }
}