{"info": {"name": "Restaurant POS - Menu Module API", "description": "Complete API collection for the Menu Module including all endpoints for menus, categories, menu items, addons, variants, and public routes", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "your_sanctum_token_here", "type": "string"}, {"key": "branch_id", "value": "1", "type": "string"}, {"key": "tenant_name", "value": "demo-restaurant", "type": "string"}], "item": [{"name": "Public API Routes", "item": [{"name": "Public Menus", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/public/menus", "host": ["{{base_url}}"], "path": ["menu", "public", "menus"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": 1,\n      \"name\": \"Main Menu\",\n      \"code\": \"MAIN\",\n      \"description\": \"Our main restaurant menu\",\n      \"menu_type\": \"main\",\n      \"start_time\": \"09:00\",\n      \"end_time\": \"23:00\",\n      \"available_days\": [0, 1, 2, 3, 4, 5, 6],\n      \"is_active\": true,\n      \"is_default\": true,\n      \"sort_order\": 1\n    }\n  ]\n}"}]}, {"name": "Public Menu by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/public/menus/1", "host": ["{{base_url}}"], "path": ["menu", "public", "menus", "1"]}}}]}, {"name": "Restaurant Public API", "item": [{"name": "Get Restaurant Info", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/info", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "info"]}}}, {"name": "Get Restaurant Menus", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/menus", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "menus"]}}}, {"name": "Get Restaurant Menu by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/menus/1", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "menus", "1"]}}}, {"name": "Get Restaurant Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/categories?menu_id=1", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "categories"], "query": [{"key": "menu_id", "value": "1"}]}}}, {"name": "Get Restaurant Menu Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/menu-items?menu_id=1&category_id=1", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "menu-items"], "query": [{"key": "menu_id", "value": "1"}, {"key": "category_id", "value": "1"}]}}}, {"name": "Get Restaurant Addons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/addons?menu_item_id=1", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "addons"], "query": [{"key": "menu_item_id", "value": "1"}]}}}, {"name": "Get Restaurant Variants", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/menu/restaurant/{{tenant_name}}/variants?menu_item_id=1", "host": ["{{base_url}}"], "path": ["menu", "restaurant", "{{tenant_name}}", "variants"], "query": [{"key": "menu_item_id", "value": "1"}]}}}]}, {"name": "Authenticated API - Menus", "item": [{"name": "List Menus", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/menus?page=1&per_page=10&search=&is_active=1", "host": ["{{base_url}}"], "path": ["api", "menu", "menus"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}, {"key": "search", "value": ""}, {"key": "is_active", "value": "1"}]}}}, {"name": "Create Menu", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"branch_id\": {{branch_id}},\n  \"name\": \"Breakfast Menu\",\n  \"code\": \"BREAKFAST\",\n  \"description\": \"Morning breakfast items available from 6 AM to 11 AM\",\n  \"menu_type\": \"breakfast\",\n  \"start_time\": \"06:00\",\n  \"end_time\": \"11:00\",\n  \"available_days\": [1, 2, 3, 4, 5],\n  \"is_active\": true,\n  \"is_default\": false,\n  \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/api/menu/menus", "host": ["{{base_url}}"], "path": ["api", "menu", "menus"]}}}, {"name": "Get Menu", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/menus/1", "host": ["{{base_url}}"], "path": ["api", "menu", "menus", "1"]}}}, {"name": "Update Menu", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Breakfast Menu\",\n  \"code\": \"BREAKFAST_UPDATED\",\n  \"description\": \"Updated morning breakfast items\",\n  \"menu_type\": \"breakfast\",\n  \"start_time\": \"06:30\",\n  \"end_time\": \"11:30\",\n  \"available_days\": [1, 2, 3, 4, 5, 6],\n  \"is_active\": true,\n  \"is_default\": false,\n  \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/api/menu/menus/1", "host": ["{{base_url}}"], "path": ["api", "menu", "menus", "1"]}}}, {"name": "Delete Menu", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/menus/1", "host": ["{{base_url}}"], "path": ["api", "menu", "menus", "1"]}}}]}, {"name": "Authenticated API - Categories", "item": [{"name": "List Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/categories?menu_id=1&page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "menu", "categories"], "query": [{"key": "menu_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"menu_id\": 1,\n  \"parent_category_id\": null,\n  \"name\": \"Appetizers\",\n  \"code\": \"APPETIZERS\",\n  \"description\": \"Start your meal with our delicious appetizers\",\n  \"icon\": \"appetizer-icon\",\n  \"image_url\": \"https://example.com/appetizers.jpg\",\n  \"sort_order\": 1,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/menu/categories", "host": ["{{base_url}}"], "path": ["api", "menu", "categories"]}}}, {"name": "Get Category", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/categories/1", "host": ["{{base_url}}"], "path": ["api", "menu", "categories", "1"]}}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Appetizers\",\n  \"code\": \"APPETIZERS_UPDATED\",\n  \"description\": \"Updated appetizer description\",\n  \"icon\": \"updated-icon\",\n  \"image_url\": \"https://example.com/updated-appetizers.jpg\",\n  \"sort_order\": 1,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/menu/categories/1", "host": ["{{base_url}}"], "path": ["api", "menu", "categories", "1"]}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/categories/1", "host": ["{{base_url}}"], "path": ["api", "menu", "categories", "1"]}}}]}, {"name": "Authenticated API - Menu Items", "item": [{"name": "List Menu Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/menu-items?menu_id=1&category_id=1&page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "menu", "menu-items"], "query": [{"key": "menu_id", "value": "1"}, {"key": "category_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"menu_id\": 1,\n  \"category_id\": 1,\n  \"name\": \"Caesar Salad\",\n  \"code\": \"CAESAR_SALAD\",\n  \"description\": \"Fresh romaine lettuce with caesar dressing, croutons, and parmesan cheese\",\n  \"short_description\": \"Classic caesar salad\",\n  \"base_price\": 12.99,\n  \"cost_price\": 4.50,\n  \"image_urls\": [\"https://example.com/caesar-salad.jpg\"],\n  \"prep_time_minutes\": 10,\n  \"calories\": 350,\n  \"nutritional_info\": {\n    \"protein\": \"8g\",\n    \"carbs\": \"15g\",\n    \"fat\": \"25g\",\n    \"fiber\": \"3g\"\n  },\n  \"allergens\": [\"eggs\", \"dairy\", \"gluten\"],\n  \"dietary_info\": [\"vegetarian\"],\n  \"barcode\": \"1234567890123\",\n  \"sku\": \"CS001\",\n  \"is_active\": true,\n  \"is_featured\": true,\n  \"is_spicy\": false,\n  \"spice_level\": null,\n  \"sort_order\": 1\n}"}, "url": {"raw": "{{base_url}}/api/menu/menu-items", "host": ["{{base_url}}"], "path": ["api", "menu", "menu-items"]}}}, {"name": "Get Menu Item", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/menu-items/1", "host": ["{{base_url}}"], "path": ["api", "menu", "menu-items", "1"]}}}, {"name": "Update <PERSON><PERSON>em", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Caesar Salad\",\n  \"code\": \"CAESAR_SALAD_UPDATED\",\n  \"description\": \"Updated fresh romaine lettuce with caesar dressing\",\n  \"short_description\": \"Updated classic caesar salad\",\n  \"base_price\": 14.99,\n  \"cost_price\": 5.00,\n  \"image_urls\": [\"https://example.com/updated-caesar-salad.jpg\"],\n  \"prep_time_minutes\": 12,\n  \"calories\": 380,\n  \"is_active\": true,\n  \"is_featured\": true,\n  \"is_spicy\": false,\n  \"sort_order\": 1\n}"}, "url": {"raw": "{{base_url}}/api/menu/menu-items/1", "host": ["{{base_url}}"], "path": ["api", "menu", "menu-items", "1"]}}}, {"name": "Delete Menu Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/menu-items/1", "host": ["{{base_url}}"], "path": ["api", "menu", "menu-items", "1"]}}}]}, {"name": "Authenticated API - Addons", "item": [{"name": "List Addons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/addons?menu_item_id=1&page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "menu", "addons"], "query": [{"key": "menu_item_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"menu_item_id\": 1,\n  \"addon_group_name\": \"Extras\",\n  \"name\": \"Extra Cheese\",\n  \"code\": \"EXTRA_CHEESE\",\n  \"price\": 2.00,\n  \"cost\": 0.50,\n  \"is_required\": false,\n  \"max_quantity\": 3,\n  \"sort_order\": 1\n}"}, "url": {"raw": "{{base_url}}/api/menu/addons", "host": ["{{base_url}}"], "path": ["api", "menu", "addons"]}}}, {"name": "Get Addon", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/addons/1", "host": ["{{base_url}}"], "path": ["api", "menu", "addons", "1"]}}}, {"name": "Update Addon", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"addon_group_name\": \"Updated Extras\",\n  \"name\": \"Updated Extra Cheese\",\n  \"code\": \"EXTRA_CHEESE_UPDATED\",\n  \"price\": 2.50,\n  \"cost\": 0.60,\n  \"is_required\": false,\n  \"max_quantity\": 5,\n  \"sort_order\": 1\n}"}, "url": {"raw": "{{base_url}}/api/menu/addons/1", "host": ["{{base_url}}"], "path": ["api", "menu", "addons", "1"]}}}, {"name": "Delete Addon", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/addons/1", "host": ["{{base_url}}"], "path": ["api", "menu", "addons", "1"]}}}]}, {"name": "Authenticated API - Variants", "item": [{"name": "List Variants", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/variants?menu_item_id=1&page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "menu", "variants"], "query": [{"key": "menu_item_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "C<PERSON> <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"menu_item_id\": 1,\n  \"name\": \"Large\",\n  \"code\": \"LARGE\",\n  \"price_modifier\": 4.00,\n  \"cost_modifier\": 1.50,\n  \"is_default\": false,\n  \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/api/menu/variants", "host": ["{{base_url}}"], "path": ["api", "menu", "variants"]}}}, {"name": "<PERSON>t", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/variants/1", "host": ["{{base_url}}"], "path": ["api", "menu", "variants", "1"]}}}, {"name": "Update <PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Large\",\n  \"code\": \"LARGE_UPDATED\",\n  \"price_modifier\": 5.00,\n  \"cost_modifier\": 2.00,\n  \"is_default\": false,\n  \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/api/menu/variants/1", "host": ["{{base_url}}"], "path": ["api", "menu", "variants", "1"]}}}, {"name": "Delete Variant", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/menu/variants/1", "host": ["{{base_url}}"], "path": ["api", "menu", "variants", "1"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set common headers", "pm.request.headers.add({", "    key: 'Accept',", "    value: 'application/json'", "});", "", "// Add timestamp for debugging", "pm.globals.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common tests for all requests", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct Content-Type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Test for successful responses", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Response has success field', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('success');", "        pm.expect(jsonData.success).to.be.true;", "    });", "}"]}}]}