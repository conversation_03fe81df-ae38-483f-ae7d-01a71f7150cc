<?php

namespace Modules\Menu\Services;

use App\Models\MenuItem;
use App\Models\MenuCategory;
use App\Models\MenuAddon;
use App\Models\MenuVariant;
use Illuminate\Support\Str;

class CodeGeneratorService
{
    /**
     * Generate unique code for menu item
     */
    public function generateMenuItemCode(string $name, ?int $categoryId = null): string
    {
        $prefix = 'MI';
        
        // Add category prefix if available
        if ($categoryId) {
            $category = MenuCategory::find($categoryId);
            if ($category && $category->code) {
                $prefix = strtoupper(substr($category->code, 0, 2)) . 'MI';
            }
        }
        
        // Generate base code from name
        $baseCode = $this->generateBaseCode($name, $prefix);
        
        // Ensure uniqueness
        return $this->ensureUniqueCode($baseCode, MenuItem::class);
    }

    /**
     * Generate unique code for category
     */
    public function generateCategoryCode(string $name, ?int $menuId = null): string
    {
        $prefix = 'CAT';
        
        // Add menu prefix if available
        if ($menuId) {
            $menu = \App\Models\Menu::find($menuId);
            if ($menu && $menu->code) {
                $prefix = strtoupper(substr($menu->code, 0, 2)) . 'CAT';
            }
        }
        
        // Generate base code from name
        $baseCode = $this->generateBaseCode($name, $prefix);
        
        // Ensure uniqueness
        return $this->ensureUniqueCode($baseCode, MenuCategory::class);
    }

    /**
     * Generate unique code for addon
     */
    public function generateAddonCode(string $name, ?int $menuItemId = null): string
    {
        $prefix = 'ADD';
        
        // Add menu item prefix if available
        if ($menuItemId) {
            $menuItem = MenuItem::find($menuItemId);
            if ($menuItem && $menuItem->code) {
                $prefix = strtoupper(substr($menuItem->code, 0, 2)) . 'ADD';
            }
        }
        
        // Generate base code from name
        $baseCode = $this->generateBaseCode($name, $prefix);
        
        // Ensure uniqueness
        return $this->ensureUniqueCode($baseCode, MenuAddon::class);
    }

    /**
     * Generate unique code for variant
     */
    public function generateVariantCode(string $name, ?int $menuItemId = null): string
    {
        $prefix = 'VAR';
        
        // Add menu item prefix if available
        if ($menuItemId) {
            $menuItem = MenuItem::find($menuItemId);
            if ($menuItem && $menuItem->code) {
                $prefix = strtoupper(substr($menuItem->code, 0, 2)) . 'VAR';
            }
        }
        
        // Generate base code from name
        $baseCode = $this->generateBaseCode($name, $prefix);
        
        // Ensure uniqueness
        return $this->ensureUniqueCode($baseCode, MenuVariant::class);
    }

    /**
     * Generate base code from name and prefix
     */
    private function generateBaseCode(string $name, string $prefix): string
    {
        // Clean and prepare the name
        $cleanName = $this->cleanArabicText($name);
        
        // If Arabic text, use transliteration or abbreviation
        if ($this->isArabic($name)) {
            $code = $this->generateArabicCode($name);
        } else {
            // For English text, use first letters of words
            $words = explode(' ', $cleanName);
            $code = '';
            foreach ($words as $word) {
                if (strlen(trim($word)) > 0) {
                    $code .= strtoupper(substr(trim($word), 0, 1));
                }
            }
            // If code is too short, add more characters
            if (strlen($code) < 3) {
                $code = strtoupper(substr(str_replace(' ', '', $cleanName), 0, 3));
            }
        }
        
        // Add timestamp suffix for uniqueness
        $timestamp = substr(time(), -3);
        
        return $prefix . '_' . $code . '_' . $timestamp;
    }

    /**
     * Generate code for Arabic text
     */
    private function generateArabicCode(string $arabicText): string
    {
        // Arabic to English mapping for common words
        $arabicToEnglish = [
            'دجاج' => 'CHK',
            'لحم' => 'MEAT',
            'سمك' => 'FISH',
            'خضار' => 'VEG',
            'فواكه' => 'FRUIT',
            'مشروب' => 'DRINK',
            'عصير' => 'JUICE',
            'قهوة' => 'COFFEE',
            'شاي' => 'TEA',
            'حلويات' => 'SWEET',
            'مقبلات' => 'APP',
            'سلطة' => 'SALAD',
            'شوربة' => 'SOUP',
            'بيتزا' => 'PIZZA',
            'برجر' => 'BURGER',
            'ساندويش' => 'SAND',
            'مكرونة' => 'PASTA',
            'أرز' => 'RICE',
            'خبز' => 'BREAD',
            'جبن' => 'CHEESE'
        ];
        
        // Check for known Arabic words
        foreach ($arabicToEnglish as $arabic => $english) {
            if (strpos($arabicText, $arabic) !== false) {
                return $english;
            }
        }
        
        // If no match found, use first 3 characters of cleaned text
        $cleaned = preg_replace('/[^\p{Arabic}]/u', '', $arabicText);
        if (mb_strlen($cleaned) >= 3) {
            // Convert Arabic characters to their position-based code
            $code = '';
            for ($i = 0; $i < min(3, mb_strlen($cleaned)); $i++) {
                $char = mb_substr($cleaned, $i, 1);
                $code .= $this->arabicCharToCode($char);
            }
            return $code;
        }
        
        return 'AR' . rand(10, 99);
    }

    /**
     * Convert Arabic character to code
     */
    private function arabicCharToCode(string $char): string
    {
        $arabicChars = [
            'ا' => 'A', 'ب' => 'B', 'ت' => 'T', 'ث' => 'TH', 'ج' => 'J',
            'ح' => 'H', 'خ' => 'KH', 'د' => 'D', 'ذ' => 'DH', 'ر' => 'R',
            'ز' => 'Z', 'س' => 'S', 'ش' => 'SH', 'ص' => 'SA', 'ض' => 'DA',
            'ط' => 'TA', 'ظ' => 'ZA', 'ع' => 'A', 'غ' => 'GH', 'ف' => 'F',
            'ق' => 'Q', 'ك' => 'K', 'ل' => 'L', 'م' => 'M', 'ن' => 'N',
            'ه' => 'H', 'و' => 'W', 'ي' => 'Y'
        ];
        
        return $arabicChars[$char] ?? 'X';
    }

    /**
     * Check if text contains Arabic characters
     */
    private function isArabic(string $text): bool
    {
        return preg_match('/[\p{Arabic}]/u', $text);
    }

    /**
     * Clean Arabic text
     */
    private function cleanArabicText(string $text): string
    {
        // Remove diacritics and special characters
        $text = preg_replace('/[\x{064B}-\x{065F}]/u', '', $text); // Remove diacritics
        $text = preg_replace('/[^\p{Arabic}\p{L}\p{N}\s]/u', '', $text); // Keep only letters, numbers, and spaces
        return trim($text);
    }

    /**
     * Ensure code is unique by adding suffix if needed
     */
    private function ensureUniqueCode(string $baseCode, string $modelClass): string
    {
        $code = $baseCode;
        $counter = 1;
        
        while ($modelClass::where('code', $code)->exists()) {
            $code = $baseCode . '_' . $counter;
            $counter++;
        }
        
        return $code;
    }
}