<?php

use Illuminate\Support\Facades\Route;
use Modules\Reports\Http\Controllers\ReportsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum', 'tenant'])->prefix('reports')->group(function () {
    // Daily Reports
    Route::get('/daily/sales-summary', [ReportsController::class, 'dailySalesSummary']);
    Route::get('/daily/stock-drawer', [ReportsController::class, 'dailyStockDrawer']);
    Route::get('/daily/payments', [ReportsController::class, 'dailyPayments']);
    Route::get('/daily/discounts', [ReportsController::class, 'dailyDiscounts']);
    Route::get('/daily/returns', [ReportsController::class, 'dailyReturns']);
    Route::get('/daily/cash-drawer', [ReportsController::class, 'dailyCashDrawer']);

    // Periodic Reports
    Route::get('/periodic/product-performance', [ReportsController::class, 'productPerformance']);
    Route::get('/periodic/staff-performance', [ReportsController::class, 'staffPerformance']);
    Route::get('/periodic/profit', [ReportsController::class, 'profitReport']);
    Route::get('/periodic/customers', [ReportsController::class, 'customerReport']);

    // Inventory Reports
    Route::get('/inventory/stock-movement', [ReportsController::class, 'stockMovement']);
    Route::get('/inventory/reorder-point', [ReportsController::class, 'reorderPoint']);

    // Restaurant-Specific Reports
    Route::get('/restaurant/menu-items', [ReportsController::class, 'menuItemsReport']);
    Route::get('/restaurant/meal-time-sales', [ReportsController::class, 'mealTimeSales']);

    // Report Management
    Route::get('/', [ReportsController::class, 'index']);
    Route::post('/generate', [ReportsController::class, 'generate']);
    Route::get('/{report}', [ReportsController::class, 'show']);
    Route::get('/{report}/download', [ReportsController::class, 'download']);
    Route::delete('/{report}', [ReportsController::class, 'destroy']);
});
