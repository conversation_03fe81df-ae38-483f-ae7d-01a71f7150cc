@extends('layouts.master')

@section('css')
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/amazeui-datetimepicker/css/amazeui.datetimepicker.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/jquery-simple-datetimepicker/jquery.simple-dtpicker.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/pickerjs/picker.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.css')}}" rel="stylesheet">
<style>
.form-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #007bff;
}
.form-section h5 {
    color: #495057;
    margin-bottom: 1rem;
}
.report-preview {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}
.loading-spinner {
    display: none;
    text-align: center;
    padding: 2rem;
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إنشاء تقرير</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ التقارير</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('reports.index') }}" class="btn btn-secondary btn-icon ml-2">
                <i class="mdi mdi-arrow-right"></i>
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">تفاصيل التقرير</h3>
            </div>
            <div class="card-body">
                <form id="reportForm" method="POST" action="{{ route('reports.store') }}">
                    @csrf
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h5><i class="mdi mdi-information mr-2"></i>المعلومات الأساسية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">اسم التقرير <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type">نوع التقرير <span class="text-danger">*</span></label>
                                    <select class="form-control select2" id="type" name="type" required>
                                        <option value="">اختر نوع التقرير</option>
                                        <optgroup label="التقارير اليومية">
                                            <option value="daily_sales_summary">ملخص المبيعات اليومية</option>
                                            <option value="daily_stock_drawer">تقرير المخزون اليومي</option>
                                            <option value="daily_payments">تقرير المدفوعات اليومية</option>
                                            <option value="daily_discounts">تقرير الخصومات اليومية</option>
                                            <option value="daily_returns">تقرير المرتجعات اليومية</option>
                                            <option value="daily_cash_drawer">تقرير الخزنة اليومية</option>
                                            <option value="hourly_sales">تقرير المبيعات بالساعة</option>
                                            <option value="tax_report">تقرير الضرائب</option>
                                        </optgroup>
                                        <optgroup label="التقارير الدورية">
                                            <option value="product_performance">أداء المنتجات</option>
                                            <option value="staff_performance">أداء الموظفين</option>
                                            <option value="profit_report">تقرير الأرباح</option>
                                            <option value="customer_report">تقرير العملاء</option>
                                        </optgroup>
                                        <optgroup label="التقارير المتقدمة">
                                            <option value="stock_movement">حركة المخزون</option>
                                            <option value="reorder_point">نقاط إعادة الطلب</option>
                                            <option value="void_cancelled">الطلبات الملغية</option>
                                            <option value="table_turnover">دوران الطاولات</option>
                                            <option value="waste_tracking">تتبع الهدر</option>
                                            <option value="supplier_performance">أداء الموردين</option>
                                        </optgroup>
                                        <optgroup label="تقارير المطعم">
                                            <option value="menu_items_report">تقرير عناصر القائمة</option>
                                            <option value="meal_time_sales">مبيعات أوقات الوجبات</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="branch_id">الفرع</label>
                                    <select class="form-control select2" id="branch_id" name="branch_id">
                                        <option value="">جميع الفروع</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="format">تنسيق التقرير <span class="text-danger">*</span></label>
                                    <select class="form-control" id="format" name="format" required>
                                        <option value="pdf">PDF</option>
                                        <option value="excel">Excel</option>
                                        <option value="csv">CSV</option>
                                        <option value="json">JSON</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Date Range Section -->
                    <div class="form-section">
                        <h5><i class="mdi mdi-calendar-range mr-2"></i>الفترة الزمنية</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="period">الفترة</label>
                                    <select class="form-control" id="period" name="period">
                                        <option value="today">اليوم</option>
                                        <option value="yesterday">أمس</option>
                                        <option value="current_week">هذا الأسبوع</option>
                                        <option value="last_week">الأسبوع الماضي</option>
                                        <option value="current_month">هذا الشهر</option>
                                        <option value="last_month">الشهر الماضي</option>
                                        <option value="current_year">هذا العام</option>
                                        <option value="last_year">العام الماضي</option>
                                        <option value="custom">فترة مخصصة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4" id="start_date_group" style="display: none;">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-4" id="end_date_group" style="display: none;">
                                <div class="form-group">
                                    <label for="end_date">تاريخ النهاية</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Parameters Section -->
                    <div class="form-section" id="advanced_parameters" style="display: none;">
                        <h5><i class="mdi mdi-settings mr-2"></i>المعاملات المتقدمة</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="include_tax" name="parameters[include_tax]" value="1">
                                        <label class="form-check-label" for="include_tax">
                                            تضمين الضرائب
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="include_discounts" name="parameters[include_discounts]" value="1">
                                        <label class="form-check-label" for="include_discounts">
                                            تضمين الخصومات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="group_by">تجميع حسب</label>
                                    <select class="form-control" id="group_by" name="parameters[group_by]">
                                        <option value="">بدون تجميع</option>
                                        <option value="day">اليوم</option>
                                        <option value="week">الأسبوع</option>
                                        <option value="month">الشهر</option>
                                        <option value="category">الفئة</option>
                                        <option value="staff">الموظف</option>
                                        <option value="payment_method">طريقة الدفع</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="meal_times">أوقات الوجبات</label>
                                    <select class="form-control select2" id="meal_times" name="parameters[meal_times][]" multiple>
                                        <option value="breakfast">الإفطار</option>
                                        <option value="lunch">الغداء</option>
                                        <option value="dinner">العشاء</option>
                                        <option value="late_night">وقت متأخر</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="mdi mdi-file-chart mr-2"></i>إنشاء التقرير
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg ml-2" onclick="previewReport()">
                            <i class="mdi mdi-eye mr-2"></i>معاينة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">معلومات التقرير</h3>
            </div>
            <div class="card-body">
                <div id="report_info">
                    <p class="text-muted">اختر نوع التقرير لعرض المعلومات</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">التقارير الأخيرة</h3>
            </div>
            <div class="card-body">
                @if($recentReports->count() > 0)
                    @foreach($recentReports as $report)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <i class="mdi mdi-file-chart text-primary" style="font-size: 1.5rem;"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $report->name }}</h6>
                                <small class="text-muted">{{ $report->created_at->diffForHumans() }}</small>
                            </div>
                            <div>
                                <a href="{{ route('reports.show', $report) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="mdi mdi-eye"></i>
                                </a>
                            </div>
                        </div>
                    @endforeach
                @else
                    <p class="text-muted">لا توجد تقارير سابقة</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">جاري التحميل...</span>
                    </div>
                    <p class="mt-3">جاري إنشاء التقرير...</p>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/jquery-ui/ui/widgets/datepicker.js')}}"></script>
<script src="{{URL::asset('assets/plugins/amazeui-datetimepicker/js/amazeui.datetimepicker.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/jquery-simple-datetimepicker/jquery.simple-dtpicker.js')}}"></script>
<script src="{{URL::asset('assets/plugins/pickerjs/picker.min.js')}}"></script>

<script>
$(document).ready(function() {
    $('.select2').select2();
    
    // Handle period change
    $('#period').change(function() {
        if ($(this).val() === 'custom') {
            $('#start_date_group, #end_date_group').show();
            $('#start_date, #end_date').prop('required', true);
        } else {
            $('#start_date_group, #end_date_group').hide();
            $('#start_date, #end_date').prop('required', false);
        }
    });
    
    // Handle report type change
    $('#type').change(function() {
        const type = $(this).val();
        updateReportInfo(type);
        
        // Show advanced parameters for certain report types
        const advancedTypes = ['product_performance', 'staff_performance', 'profit_report', 'meal_time_sales'];
        if (advancedTypes.includes(type)) {
            $('#advanced_parameters').show();
        } else {
            $('#advanced_parameters').hide();
        }
    });
    
    // Pre-select report type from URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const reportType = urlParams.get('type');
    if (reportType) {
        $('#type').val(reportType).trigger('change');
        $('#name').val(getReportName(reportType));
    }
    
    // Handle form submission
    $('#reportForm').submit(function(e) {
        e.preventDefault();
        $('#loadingModal').modal('show');
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                $('#loadingModal').modal('hide');
                if (response.success) {
                    window.location.href = `/reports/${response.data.id}`;
                } else {
                    alert('حدث خطأ في إنشاء التقرير');
                }
            },
            error: function() {
                $('#loadingModal').modal('hide');
                alert('حدث خطأ في إنشاء التقرير');
            }
        });
    });
});

function updateReportInfo(type) {
    const reportDescriptions = {
        'daily_sales_summary': 'تقرير شامل للمبيعات اليومية يتضمن إجمالي المبيعات، عدد الطلبات، متوسط قيمة الطلب، والضرائب المحصلة.',
        'daily_stock_drawer': 'تقرير يوضح حالة المخزون في نهاية اليوم، الكميات المباعة، والكميات المتبقية لكل منتج.',
        'daily_payments': 'تفصيل شامل لجميع المدفوعات المحصلة خلال اليوم مقسمة حسب طرق الدفع المختلفة.',
        'hourly_sales': 'تحليل المبيعات على مدار ساعات اليوم لتحديد أوقات الذروة والهدوء.',
        'tax_report': 'تقرير مفصل للضرائب المحصلة مقسمة حسب معدلات الضريبة المختلفة.',
        'void_cancelled': 'تحليل الطلبات الملغية والمرفوضة مع أسباب الإلغاء وتأثيرها على المبيعات.',
        'table_turnover': 'تقرير كفاءة استخدام الطاولات ومعدل دوران العملاء في المطعم.',
        'waste_tracking': 'تتبع المواد المهدرة وتكلفتها مع تحليل أسباب الهدر.',
        'supplier_performance': 'تقييم أداء الموردين من حيث جودة التسليم والالتزام بالمواعيد.'
    };
    
    const description = reportDescriptions[type] || 'اختر نوع التقرير لعرض المعلومات';
    $('#report_info').html(`<p>${description}</p>`);
}

function getReportName(type) {
    const reportNames = {
        'daily_sales_summary': 'ملخص المبيعات اليومية',
        'daily_stock_drawer': 'تقرير المخزون اليومي',
        'daily_payments': 'تقرير المدفوعات اليومية',
        'hourly_sales': 'تقرير المبيعات بالساعة',
        'tax_report': 'تقرير الضرائب',
        'void_cancelled': 'تقرير الطلبات الملغية',
        'table_turnover': 'تقرير دوران الطاولات',
        'waste_tracking': 'تقرير تتبع الهدر',
        'supplier_performance': 'تقرير أداء الموردين'
    };
    
    return reportNames[type] || 'تقرير جديد';
}

function previewReport() {
    // Implementation for report preview
    alert('معاينة التقرير - قيد التطوير');
}
</script>
@endsection
