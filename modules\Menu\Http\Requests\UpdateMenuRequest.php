<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMenuRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:100',
            'code' => 'sometimes|string|max:50',
            'description' => 'nullable|string',
            'menu_type' => 'nullable|string|max:50',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'available_days' => 'nullable|array',
            'available_days.*' => 'integer|between:0,6',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.string' => 'اسم القائمة يجب أن يكون نص',
            'name.max' => 'اسم القائمة يجب ألا يتجاوز 100 حرف',
            'code.string' => 'كود القائمة يجب أن يكون نص',
            'code.max' => 'كود القائمة يجب ألا يتجاوز 50 حرف',
            'description.string' => 'الوصف يجب أن يكون نص',
            'menu_type.string' => 'نوع القائمة يجب أن يكون نص',
            'menu_type.max' => 'نوع القائمة يجب ألا يتجاوز 50 حرف',
            'start_time.date_format' => 'وقت البداية يجب أن يكون بصيغة H:i',
            'end_time.date_format' => 'وقت النهاية يجب أن يكون بصيغة H:i',
            'end_time.after' => 'وقت النهاية يجب أن يكون بعد وقت البداية',
            'available_days.array' => 'الأيام المتاحة يجب أن تكون مصفوفة',
            'available_days.*.integer' => 'اليوم يجب أن يكون رقم صحيح',
            'available_days.*.between' => 'الأيام المتاحة يجب أن تكون بين 0 (الأحد) و 6 (السبت)',
            'is_active.boolean' => 'حالة النشاط يجب أن تكون صحيح أو خطأ',
            'is_default.boolean' => 'حالة الافتراضي يجب أن تكون صحيح أو خطأ',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}