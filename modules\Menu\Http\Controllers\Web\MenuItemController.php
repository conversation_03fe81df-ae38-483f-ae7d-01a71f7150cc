<?php

namespace Modules\Menu\Http\Controllers\Web;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;use Modules\Menu\Http\Requests\StoreMenuItemRequest;
use Modules\Menu\Http\Requests\UpdateMenuItemRequest;
use Modules\Menu\Services\MenuItemService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class MenuItemController extends Controller
{
    protected $menuItemService;

    public function __construct(MenuItemService $menuItemService)
    {
        $this->menuItemService = $menuItemService;
    }

    /**
     * Display a listing of menu items for DataTable
     */
    public function index(Request $request)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            if ($request->ajax()) {
                return $this->menuItemService->getMenuItemsForDataTable($branchId, $request);
            }

            return view('menu::menu-items');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu items: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created menu item
     */
    public function store(StoreMenuItemRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $menuItem = $this->menuItemService->createMenuItemForWeb($data);
            
            return ResponseHelper::success('Menu item created successfully', $menuItem);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create menu item: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified menu item
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->getMenuItemByIdForBranch($id, $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success('Menu item retrieved successfully', $menuItem);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu item: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified menu item
     */
    public function edit(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->getMenuItemByIdForBranch($id, $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success('Menu item retrieved for editing', $menuItem);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu item: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified menu item
     */
    public function update(UpdateMenuItemRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->updateMenuItemForWeb($id, $request->validated(), $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success('Menu item updated successfully', $menuItem);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update menu item: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified menu item
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->menuItemService->deleteMenuItemForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success('Menu item deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete menu item: ' . $e->getMessage());
        }
    }

    /**
     * Get menu items list for dropdowns
     */
    public function getMenuItemsList(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $categoryId = $request->get('category_id');
            $menuItems = $this->menuItemService->getMenuItemsListForBranch($branchId, $categoryId);
            
            return ResponseHelper::success('Menu items list retrieved successfully', $menuItems);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu items list: ' . $e->getMessage());
        }
    }
}