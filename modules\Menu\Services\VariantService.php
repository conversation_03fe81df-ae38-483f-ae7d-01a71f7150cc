<?php

namespace Modules\Menu\Services;

use App\Models\MenuItemVariant;
use Illuminate\Pagination\LengthAwarePaginator;

class VariantService
{
    protected $codeGenerator;

    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    /**
     * Get variants for a specific branch with pagination and filtering.
     */
    public function getVariantsForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuItemVariant::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_item_id'])) {
            $query->where('menu_item_id', $filters['menu_item_id']);
        }

        if (isset($filters['is_default'])) {
            $query->where('is_default', $filters['is_default']);
        }

        if (isset($filters['min_price_modifier'])) {
            $query->where('price_modifier', '>=', $filters['min_price_modifier']);
        }

        if (isset($filters['max_price_modifier'])) {
            $query->where('price_modifier', '<=', $filters['max_price_modifier']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get variant by ID with relationships.
     */
    public function getVariantById(int $id): ?MenuItemVariant
    {
        return MenuItemVariant::with(['menuItem'])
            ->find($id);
    }

    /**
     * Get variant by ID for a specific branch.
     */
    public function getVariantByIdForBranch(int $id, int $branchId): ?MenuItemVariant
    {
        return MenuItemVariant::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Create a new variant.
     */
    public function createVariant(array $data): MenuItemVariant
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateVariantCode(
                $data['name'], 
                $data['menu_item_id'] ?? null
            );
        }

        return MenuItemVariant::create($data);
    }

    /**
     * Update an existing variant.
     */
    public function updateVariant(int $id, array $data): ?MenuItemVariant
    {
        $variant = MenuItemVariant::find($id);
        
        if (!$variant) {
            return null;
        }

        $variant->update($data);
        
        return $variant->fresh(['menuItem']);
    }

    /**
     * Update an existing variant for a specific branch.
     */
    public function updateVariantForBranch(int $id, array $data, int $branchId): ?MenuItemVariant
    {
        $variant = MenuItemVariant::whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$variant) {
            return null;
        }

        $variant->update($data);
        
        return $variant->fresh(['menuItem']);
    }

    /**
     * Delete a variant.
     */
    public function deleteVariant(int $id): bool
    {
        $variant = MenuItemVariant::find($id);
        
        if (!$variant) {
            return false;
        }

        return $variant->delete();
    }

    /**
     * Delete a variant for a specific branch.
     */
    public function deleteVariantForBranch(int $id, int $branchId): bool
    {
        $variant = MenuItemVariant::whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$variant) {
            return false;
        }

        return $variant->delete();
    }

    /**
     * Get variants for a menu item.
     */
    public function getVariantsForMenuItem(int $menuItemId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItemVariant::where('menu_item_id', $menuItemId)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get default variant for a menu item.
     */
    public function getDefaultVariantForMenuItem(int $menuItemId): ?MenuItemVariant
    {
        return MenuItemVariant::where('menu_item_id', $menuItemId)
            ->where('is_default', true)
            ->first();
    }

    /**
     * Set default variant for a menu item (unsets other defaults).
     */
    public function setDefaultVariant(int $variantId): bool
    {
        $variant = MenuItemVariant::find($variantId);
        
        if (!$variant) {
            return false;
        }

        // Unset other defaults for the same menu item
        MenuItemVariant::where('menu_item_id', $variant->menu_item_id)
            ->where('id', '!=', $variantId)
            ->update(['is_default' => false]);

        // Set this variant as default
        $variant->update(['is_default' => true]);

        return true;
    }

    /**
     * Create variant for web interface.
     */
    public function createVariantForWeb(array $data): MenuItemVariant
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateVariantCode(
                $data['name'], 
                $data['menu_item_id'] ?? null
            );
        }

        $variant = MenuItemVariant::create($data);
        return $variant->load(['menuItem']);
    }

    /**
     * Update variant for web interface.
     */
    public function updateVariantForWeb(int $id, array $data, int $branchId): ?MenuItemVariant
    {
        return $this->updateVariantForBranch($id, $data, $branchId);
    }

    /**
     * Get variants for DataTable.
     */
    public function getVariantsForDataTable(int $branchId, \Illuminate\Http\Request $request): \Illuminate\Http\JsonResponse
    {
        $query = MenuItemVariant::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply search
        if ($request->has('search') && !empty($request->search['value'])) {
            $search = $request->search['value'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Apply ordering
        if ($request->has('order')) {
            $columns = ['id', 'name', 'type', 'price', 'is_active', 'created_at'];
            $orderColumn = $columns[$request->order[0]['column']] ?? 'id';
            $orderDirection = $request->order[0]['dir'] ?? 'asc';
            $query->orderBy($orderColumn, $orderDirection);
        }

        $totalRecords = $query->count();
        
        // Apply pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $variants = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = $variants->map(function ($variant, $index) use ($start) {
            // Map type to Arabic
            $typeMap = [
                'size' => 'الحجم',
                'color' => 'اللون',
                'flavor' => 'النكهة',
                'temperature' => 'درجة الحرارة',
                'spice_level' => 'مستوى التوابل',
                'other' => 'أخرى'
            ];
            
            return [
                'DT_RowIndex' => $start + $index + 1,
                'name' => $variant->name,
                'type' => $typeMap[$variant->type] ?? $variant->type ?? '-',
                'price' => number_format($variant->price, 2) . ' ريال',
                'is_active' => $variant->is_active ? 
                    '<span class="badge badge-success">نشط</span>' : 
                    '<span class="badge badge-danger">غير نشط</span>',
                'created_at' => $variant->created_at->format('Y-m-d H:i:s'),
                'action' => $this->getActionButtons($variant->id)
            ];
        });

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    /**
     * Generate action buttons for DataTable.
     */
    private function getActionButtons(int $id): string
    {
        return '
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-info show-variation" data-id="' . $id . '" title="عرض">
                    <i class="fa fa-eye"></i>
                </button>
                <button type="button" class="btn btn-sm btn-primary edit-variation" data-id="' . $id . '" title="تعديل">
                    <i class="fa fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger delete-variation" data-id="' . $id . '" title="حذف">
                    <i class="fa fa-trash"></i>
                </button>
            </div>
        ';
    }
}