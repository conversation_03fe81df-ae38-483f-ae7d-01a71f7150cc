<?php

namespace Modules\Reports\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GenerateReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:sales,inventory,staff,customer,profit,payment,discount,return,cash_drawer,product_performance,stock_movement,reorder_point,menu_items,meal_time_sales,tax_report,void_cancelled,hourly_sales,table_turnover,waste_tracking,supplier_performance',
            'branch_id' => 'nullable|exists:branches,id',
            'format' => 'required|string|in:pdf,excel,csv,json',
            'period' => 'nullable|string|in:today,yesterday,current_week,last_week,current_month,last_month,current_year,last_year,custom',
            'start_date' => 'nullable|date|required_if:period,custom',
            'end_date' => 'nullable|date|after_or_equal:start_date|required_if:period,custom',
            'parameters' => 'nullable|array',
            'parameters.include_tax' => 'nullable|boolean',
            'parameters.include_discounts' => 'nullable|boolean',
            'parameters.group_by' => 'nullable|string|in:day,week,month,category,staff,payment_method',
            'parameters.meal_times' => 'nullable|array',
            'parameters.meal_times.*' => 'string|in:breakfast,lunch,dinner,late_night',
            'parameters.payment_methods' => 'nullable|array',
            'parameters.payment_methods.*' => 'exists:payment_methods,id',
            'parameters.categories' => 'nullable|array',
            'parameters.categories.*' => 'exists:menu_categories,id',
            'parameters.staff_ids' => 'nullable|array',
            'parameters.staff_ids.*' => 'exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'type.in' => 'The selected report type is invalid.',
            'format.in' => 'The selected format is invalid. Supported formats are: pdf, excel, csv, json.',
            'period.in' => 'The selected period is invalid.',
            'start_date.required_if' => 'Start date is required when period is custom.',
            'end_date.required_if' => 'End date is required when period is custom.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'branch_id.exists' => 'The selected branch does not exist.',
            'parameters.meal_times.*.in' => 'Invalid meal time specified.',
            'parameters.payment_methods.*.exists' => 'One or more payment methods do not exist.',
            'parameters.categories.*.exists' => 'One or more categories do not exist.',
            'parameters.staff_ids.*.exists' => 'One or more staff members do not exist.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values if not provided
        if (!$this->has('format')) {
            $this->merge(['format' => 'pdf']);
        }

        if (!$this->has('period')) {
            $this->merge(['period' => 'today']);
        }

        // If branch_id is provided, ensure it belongs to the current tenant
        if ($this->has('branch_id') && $this->branch_id) {
            $tenant = $this->get('tenant');
            if ($tenant) {
                $branch = $tenant->branches()->find($this->branch_id);
                if (!$branch) {
                    $this->merge(['branch_id' => null]);
                }
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation logic can be added here
            $tenant = $this->get('tenant');
            
            if (!$tenant) {
                $validator->errors()->add('tenant', 'Tenant context is required.');
                return;
            }

            // Validate branch belongs to tenant if provided
            if ($this->branch_id) {
                $branch = $tenant->branches()->find($this->branch_id);
                if (!$branch) {
                    $validator->errors()->add('branch_id', 'The selected branch does not belong to your tenant.');
                }
            }

            // Validate date range
            if ($this->period === 'custom') {
                if ($this->start_date && $this->end_date) {
                    $startDate = \Carbon\Carbon::parse($this->start_date);
                    $endDate = \Carbon\Carbon::parse($this->end_date);
                    
                    // Check if date range is not too large (e.g., more than 1 year)
                    if ($startDate->diffInDays($endDate) > 365) {
                        $validator->errors()->add('end_date', 'Date range cannot exceed 365 days.');
                    }
                }
            }
        });
    }
}
